<template>
  <div class="enhanced-import-view">
    <!-- 页面标题 -->
    <div class="import-header">
      <h1 class="page-title">导入图书</h1>
      <p class="page-description">填写图书详细信息并上传封面</p>
    </div>

    <div class="import-content">
      <!-- 步骤指示器 -->
      <el-steps :active="currentStep" align-center class="import-steps">
        <el-step title="选择文件" />
        <el-step title="填写信息" />
        <el-step title="确认导入" />
      </el-steps>

      <!-- 步骤1：文件选择 -->
      <div v-if="currentStep === 0" class="step-content">
        <el-card>
          <template #header>
            <span>选择电子书文件</span>
          </template>

          <div class="file-selector">
            <div class="upload-area" @click="selectBookFile">
              <el-icon class="upload-icon"><Upload /></el-icon>
              <div class="upload-text">
                <p>点击选择电子书文件</p>
                <p class="upload-hint">支持 EPUB、PDF、TXT、MOBI、AZW3 格式</p>
              </div>
            </div>

            <div v-if="selectedFile" class="selected-file">
              <el-icon><Document /></el-icon>
              <span>{{ selectedFile.name }}</span>
              <el-button type="text" @click="removeFile">移除</el-button>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 步骤2：信息填写 -->
      <div v-if="currentStep === 1" class="step-content">
        <div class="form-layout">
          <!-- 左侧：封面上传 -->
          <div class="cover-section">
            <el-card>
              <template #header>
                <span>图书封面</span>
              </template>

              <div class="cover-upload">
                <div class="cover-uploader" @click="selectCoverFile">
                  <img v-if="coverPreview" :src="coverPreview" class="cover-image" />
                  <div v-else class="cover-placeholder">
                    <el-icon><Plus /></el-icon>
                    <div class="upload-text">选择封面</div>
                  </div>
                </div>

                <div class="cover-actions">
                  <el-button v-if="coverPreview" size="small" @click="removeCover">
                    移除封面
                  </el-button>
                  <el-button size="small" type="primary" @click="extractCover" :loading="extractingCover">
                    从文件提取
                  </el-button>
                </div>
              </div>
            </el-card>
          </div>

          <!-- 右侧：信息表单 -->
          <div class="info-section">
            <el-card>
              <template #header>
                <span>图书信息</span>
              </template>

              <el-form :model="bookForm" :rules="formRules" ref="bookFormRef" label-width="80px">
                <el-form-item label="书名" prop="title" required>
                  <el-input v-model="bookForm.title" placeholder="请输入书名" />
                </el-form-item>

                <el-form-item label="作者" prop="author">
                  <el-input v-model="bookForm.author" placeholder="请输入作者" />
                </el-form-item>

                <el-form-item label="ISBN" prop="isbn">
                  <el-input v-model="bookForm.isbn" placeholder="请输入ISBN" />
                </el-form-item>

                <el-form-item label="出版社" prop="publisher">
                  <el-input v-model="bookForm.publisher" placeholder="请输入出版社" />
                </el-form-item>

                <el-form-item label="出版日期" prop="publishDate">
                  <el-date-picker
                    v-model="bookForm.publishDate"
                    type="date"
                    placeholder="选择出版日期"
                    style="width: 100%"
                  />
                </el-form-item>

                <el-form-item label="语言" prop="language">
                  <el-select v-model="bookForm.language" style="width: 100%">
                    <el-option label="中文" value="zh-CN" />
                    <el-option label="英文" value="en-US" />
                    <el-option label="日文" value="ja-JP" />
                    <el-option label="其他" value="other" />
                  </el-select>
                </el-form-item>

                <el-form-item label="描述" prop="description">
                  <el-input
                    v-model="bookForm.description"
                    type="textarea"
                    :rows="4"
                    placeholder="请输入图书描述"
                  />
                </el-form-item>

                <el-form-item label="标签" prop="tags">
                  <el-tag
                    v-for="tag in bookForm.tags"
                    :key="tag"
                    closable
                    @close="removeTag(tag)"
                    class="tag-item"
                  >
                    {{ tag }}
                  </el-tag>
                  <el-input
                    v-if="inputVisible"
                    ref="inputRef"
                    v-model="inputValue"
                    size="small"
                    class="tag-input"
                    @keyup.enter="handleInputConfirm"
                    @blur="handleInputConfirm"
                  />
                  <el-button v-else size="small" @click="showInput">
                    + 添加标签
                  </el-button>
                </el-form-item>
              </el-form>
            </el-card>
          </div>
        </div>
      </div>

      <!-- 步骤3：确认导入 -->
      <div v-if="currentStep === 2" class="step-content">
        <el-card>
          <template #header>
            <span>确认导入信息</span>
          </template>

          <div class="confirm-layout">
            <div class="preview-cover">
              <img v-if="coverPreview" :src="coverPreview" class="preview-image" />
              <div v-else class="no-cover">无封面</div>
            </div>

            <div class="preview-info">
              <h3>{{ bookForm.title || '未填写书名' }}</h3>
              <p><strong>作者：</strong>{{ bookForm.author || '未知' }}</p>
              <p><strong>出版社：</strong>{{ bookForm.publisher || '未知' }}</p>
              <p><strong>语言：</strong>{{ getLanguageLabel(bookForm.language) }}</p>
              <p><strong>文件：</strong>{{ selectedFile?.name }}</p>
              <p v-if="bookForm.description"><strong>描述：</strong>{{ bookForm.description }}</p>
              <div v-if="bookForm.tags.length > 0" class="preview-tags">
                <strong>标签：</strong>
                <el-tag v-for="tag in bookForm.tags" :key="tag" size="small" class="preview-tag">
                  {{ tag }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 导入进度 -->
      <div v-if="importing" class="import-progress">
        <el-card>
          <template #header>
            <span>导入进度</span>
          </template>

          <div class="progress-content">
            <el-progress
              :percentage="importProgress"
              :status="importStatus"
            />
            <p class="progress-text">{{ progressText }}</p>
          </div>
        </el-card>
      </div>

      <!-- 导入结果 -->
      <div v-if="importResults.length > 0" class="import-results">
        <el-card>
          <template #header>
            <span>导入结果</span>
          </template>

          <div class="results-content">
            <div v-for="result in importResults" :key="result.file" class="result-item">
              <el-icon v-if="result.success" class="success-icon" color="#67C23A">
                <SuccessFilled />
              </el-icon>
              <el-icon v-else class="error-icon" color="#F56C6C">
                <Warning />
              </el-icon>
              <span class="result-text">{{ result.message }}</span>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 操作按钮 -->
      <div class="step-actions">
        <el-button v-if="currentStep > 0 && !importing" @click="prevStep">上一步</el-button>
        <el-button v-if="currentStep < 2 && !importing" type="primary" @click="nextStep" :disabled="!canProceed">
          下一步
        </el-button>
        <el-button v-if="currentStep === 2 && !importing" type="primary" :loading="importing" @click="confirmImport">
          确认导入
        </el-button>
        <el-button v-if="importResults.length > 0 && importStatus === 'success'" @click="resetImport">
          继续导入
        </el-button>
        <el-button v-if="importResults.length > 0" type="primary" @click="goToLibrary">
          查看图书列表
        </el-button>
        <el-button v-if="importResults.length > 0" @click="goBack">
          返回
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, FormInstance } from 'element-plus'
import { Upload, SuccessFilled, Warning, Document, Plus } from '@element-plus/icons-vue'

const router = useRouter()

// 响应式数据
const currentStep = ref(0)
const importing = ref(false)
const extractingCover = ref(false)
const importProgress = ref(0)
const importStatus = ref<'success' | 'exception' | undefined>(undefined)
const progressText = ref('')
const importResults = ref<Array<{file: string, success: boolean, message: string}>>([])

// 文件相关
const selectedFile = ref<File | null>(null)
const selectedCoverFile = ref<File | null>(null)
const coverPreview = ref<string>('')

// 表单相关
const bookFormRef = ref<FormInstance>()
const bookForm = ref({
  title: '',
  author: '',
  isbn: '',
  publisher: '',
  publishDate: null as Date | null,
  language: 'zh-CN',
  description: '',
  tags: [] as string[]
})

// 标签输入相关
const inputVisible = ref(false)
const inputValue = ref('')
const inputRef = ref()

// 表单验证规则
const formRules = {
  title: [
    { required: true, message: '请输入书名', trigger: 'blur' },
    { min: 1, max: 200, message: '书名长度应在1-200个字符之间', trigger: 'blur' }
  ],
  author: [
    { max: 100, message: '作者名称不能超过100个字符', trigger: 'blur' }
  ],
  isbn: [
    {
      pattern: /^(?:ISBN[-:\s]?)?(?:97[89][-\s]?)?[\d\-\s]{8,17}[\dX]?$/i,
      message: '请输入有效的ISBN格式（如：978-7-111-12345-6 或 7-111-12345-X）',
      trigger: 'blur'
    }
  ],
  publisher: [
    { max: 100, message: '出版社名称不能超过100个字符', trigger: 'blur' }
  ],
  description: [
    { max: 2000, message: '描述不能超过2000个字符', trigger: 'blur' }
  ]
}

// 计算属性
const canProceed = computed(() => {
  if (currentStep.value === 0) {
    return selectedFile.value !== null
  }
  if (currentStep.value === 1) {
    return bookForm.value.title.trim() !== ''
  }
  return true
})

// 步骤控制方法
const nextStep = async () => {
  if (currentStep.value === 1) {
    // 验证表单
    if (!bookFormRef.value) return

    try {
      await bookFormRef.value.validate()
      currentStep.value++
    } catch (error) {
      ElMessage.warning('请完善必填信息')
      return
    }
  } else {
    currentStep.value++
  }
}

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

// 文件处理方法
const selectBookFile = async () => {
  try {
    const filters = [
      { name: '电子书文件', extensions: ['epub', 'pdf', 'txt', 'mobi', 'azw3'] },
      { name: 'EPUB', extensions: ['epub'] },
      { name: 'PDF', extensions: ['pdf'] },
      { name: '文本文件', extensions: ['txt'] },
      { name: 'MOBI', extensions: ['mobi'] },
      { name: '所有文件', extensions: ['*'] }
    ]

    const filePaths = await window.electronAPI.file.select(filters)
    if (filePaths && filePaths.length > 0) {
      const filePath = filePaths[0]

      // 创建一个模拟的文件对象
      selectedFile.value = {
        name: filePath.split(/[/\\]/).pop() || 'unknown',
        path: filePath,
        size: 0 // 实际大小会在后端获取
      } as any

      // 自动填充书名（从文件名提取）
      if (!bookForm.value.title) {
        const fileName = selectedFile.value.name.replace(/\.[^/.]+$/, '') // 移除扩展名
        bookForm.value.title = fileName
      }

      ElMessage.success('文件选择成功')
    }
  } catch (error) {
    console.error('选择文件失败:', error)
    ElMessage.error('选择文件失败')
  }
}

const handleFileSelect = (uploadFile: any) => {
  // 保留原有的拖拽上传处理逻辑（如果需要）
  const file = uploadFile.raw
  if (!file) return

  // 验证文件类型
  const allowedTypes = ['epub', 'pdf', 'txt', 'mobi', 'azw3']
  const fileExtension = file.name.split('.').pop()?.toLowerCase()

  if (!fileExtension || !allowedTypes.includes(fileExtension)) {
    ElMessage.error('不支持的文件格式')
    return
  }

  selectedFile.value = file

  // 自动填充书名（从文件名提取）
  if (!bookForm.value.title) {
    const fileName = file.name.replace(/\.[^/.]+$/, '') // 移除扩展名
    bookForm.value.title = fileName
  }

  ElMessage.success('文件选择成功')
}

const removeFile = () => {
  selectedFile.value = null
  // 重置到第一步
  currentStep.value = 0
}

// 封面处理方法
const selectCoverFile = async () => {
  try {
    const filters = [
      { name: '图片文件', extensions: ['jpg', 'jpeg', 'png', 'gif', 'webp'] },
      { name: 'JPEG', extensions: ['jpg', 'jpeg'] },
      { name: 'PNG', extensions: ['png'] },
      { name: '所有文件', extensions: ['*'] }
    ]

    const filePaths = await window.electronAPI.file.select(filters)
    if (filePaths && filePaths.length > 0) {
      const filePath = filePaths[0]

      // 读取文件并转换为base64
      try {
        const imageData = await window.electronAPI.file.readAsBase64(filePath)

        // 创建一个模拟的文件对象
        selectedCoverFile.value = {
          name: filePath.split(/[/\\]/).pop() || 'cover',
          path: filePath,
          size: 0
        } as any

        // 创建预览 - 使用base64数据
        coverPreview.value = `data:image/jpeg;base64,${imageData}`

        ElMessage.success('封面选择成功')
      } catch (readError) {
        console.error('读取图片文件失败:', readError)
        ElMessage.error('读取图片文件失败')
      }
    }
  } catch (error) {
    console.error('选择封面失败:', error)
    ElMessage.error('选择封面失败')
  }
}

const handleCoverSelect = (uploadFile: any) => {
  // 保留原有的拖拽上传处理逻辑（如果需要）
  const file = uploadFile.raw
  if (!file) return

  // 验证文件类型
  if (!file.type.startsWith('image/')) {
    ElMessage.error('请选择图片文件')
    return
  }

  // 验证文件大小（限制为5MB）
  if (file.size > 5 * 1024 * 1024) {
    ElMessage.error('图片文件不能超过5MB')
    return
  }

  selectedCoverFile.value = file

  // 创建预览
  const reader = new FileReader()
  reader.onload = (e) => {
    coverPreview.value = e.target?.result as string
  }
  reader.readAsDataURL(file)

  ElMessage.success('封面上传成功')
}

const removeCover = () => {
  selectedCoverFile.value = null
  coverPreview.value = ''
}

// 从文件提取封面和元数据
const extractCover = async () => {
  if (!selectedFile.value) {
    ElMessage.warning('请先选择图书文件')
    return
  }

  // 检查文件格式是否支持元数据提取
  const fileName = selectedFile.value.name.toLowerCase()
  if (!fileName.endsWith('.epub')) {
    ElMessage.info('当前仅支持从EPUB格式提取元数据')
    return
  }

  extractingCover.value = true

  try {
    // 调用后端API提取元数据
    const metadata = await window.electronAPI.book.extractMetadata(selectedFile.value.path)

    let extractedCount = 0

    // 更新表单数据（只在字段为空时更新）
    if (metadata.title && !bookForm.value.title.trim()) {
      bookForm.value.title = metadata.title
      extractedCount++
    }
    if (metadata.author && !bookForm.value.author.trim()) {
      bookForm.value.author = metadata.author
      extractedCount++
    }
    if (metadata.publisher && !bookForm.value.publisher.trim()) {
      bookForm.value.publisher = metadata.publisher
      extractedCount++
    }
    if (metadata.description && !bookForm.value.description.trim()) {
      bookForm.value.description = metadata.description
      extractedCount++
    }
    if (metadata.language && bookForm.value.language === 'zh-CN') {
      bookForm.value.language = metadata.language
      extractedCount++
    }

    // 处理封面
    if (metadata.coverData) {
      const blob = new Blob([metadata.coverData], { type: 'image/jpeg' })
      const url = URL.createObjectURL(blob)
      coverPreview.value = url
      selectedCoverFile.value = new File([metadata.coverData], 'cover.jpg', { type: 'image/jpeg' })
      extractedCount++
    }

    // 提供详细的反馈
    if (extractedCount > 0) {
      const items = []
      if (metadata.title) items.push('标题')
      if (metadata.author) items.push('作者')
      if (metadata.publisher) items.push('出版社')
      if (metadata.description) items.push('描述')
      if (metadata.language) items.push('语言')
      if (metadata.coverData) items.push('封面')

      ElMessage.success(`成功提取：${items.join('、')}`)
    } else {
      ElMessage.info('未找到可提取的元数据信息')
    }
  } catch (error) {
    console.error('提取信息失败:', error)
    const errorMessage = error instanceof Error ? error.message : '未知错误'

    // 根据错误类型提供更友好的提示
    if (errorMessage.includes('文件不存在')) {
      ElMessage.error('文件不存在，请重新选择文件')
    } else if (errorMessage.includes('格式不支持')) {
      ElMessage.error('不支持的文件格式')
    } else if (errorMessage.includes('文件损坏')) {
      ElMessage.error('文件可能已损坏，无法读取')
    } else {
      ElMessage.error(`提取信息失败: ${errorMessage}`)
    }
  } finally {
    extractingCover.value = false
  }
}

// 标签处理方法
const removeTag = (tag: string) => {
  const index = bookForm.value.tags.indexOf(tag)
  if (index > -1) {
    bookForm.value.tags.splice(index, 1)
  }
}

const showInput = () => {
  inputVisible.value = true
  nextTick(() => {
    inputRef.value?.focus()
  })
}

const handleInputConfirm = () => {
  const value = inputValue.value.trim()
  if (value && !bookForm.value.tags.includes(value)) {
    bookForm.value.tags.push(value)
  }
  inputVisible.value = false
  inputValue.value = ''
}

// 工具方法
const getLanguageLabel = (language: string) => {
  const labels: Record<string, string> = {
    'zh-CN': '中文',
    'en-US': '英文',
    'ja-JP': '日文',
    'other': '其他'
  }
  return labels[language] || language
}

// 导入相关方法
const confirmImport = async () => {
  if (!selectedFile.value) {
    ElMessage.error('请选择文件')
    return
  }

  importing.value = true
  importProgress.value = 0
  importStatus.value = undefined
  progressText.value = '开始导入...'

  try {
    progressText.value = '正在处理文件...'
    importProgress.value = 20

    // 准备导入数据
    const importData = {
      filePath: selectedFile.value.path || selectedFile.value.name,
      title: bookForm.value.title,
      author: bookForm.value.author,
      isbn: bookForm.value.isbn,
      publisher: bookForm.value.publisher,
      publishDate: bookForm.value.publishDate?.toISOString().split('T')[0],
      language: bookForm.value.language,
      description: bookForm.value.description,
      tags: bookForm.value.tags,
      coverFile: selectedCoverFile.value?.path || null // 如果有封面文件
    }

    progressText.value = '正在导入图书...'
    importProgress.value = 60

    // 调用增强的导入API
    const result = await window.electronAPI.book.addWithDetails(importData)

    importProgress.value = 100
    importStatus.value = 'success'
    progressText.value = '导入完成'

    importResults.value = [{
      file: selectedFile.value.name,
      success: true,
      message: `${selectedFile.value.name} 导入成功`
    }]

    ElMessage.success('图书导入成功')

  } catch (error) {
    console.error('导入失败:', error)
    importStatus.value = 'exception'
    progressText.value = '导入失败'

    const errorMessage = error instanceof Error ? error.message : '未知错误'
    let userFriendlyMessage = errorMessage

    // 根据错误类型提供更友好的提示
    if (errorMessage.includes('文件不存在')) {
      userFriendlyMessage = '文件不存在，请检查文件路径'
    } else if (errorMessage.includes('文件已经导入')) {
      userFriendlyMessage = '该文件已经导入过了'
    } else if (errorMessage.includes('不支持的格式')) {
      userFriendlyMessage = '不支持的文件格式'
    } else if (errorMessage.includes('文件损坏')) {
      userFriendlyMessage = '文件可能已损坏，无法读取'
    } else if (errorMessage.includes('磁盘空间不足')) {
      userFriendlyMessage = '磁盘空间不足，请清理后重试'
    } else if (errorMessage.includes('权限')) {
      userFriendlyMessage = '文件访问权限不足'
    }

    importResults.value = [{
      file: selectedFile.value?.name || '未知文件',
      success: false,
      message: userFriendlyMessage
    }]

    ElMessage.error(userFriendlyMessage)
  } finally {
    importing.value = false
  }
}

// 重置导入状态，开始新的导入
const resetImport = () => {
  // 重置所有状态
  currentStep.value = 0
  importing.value = false
  extractingCover.value = false
  importProgress.value = 0
  importStatus.value = undefined
  progressText.value = ''
  importResults.value = []

  // 清空文件选择
  selectedFile.value = null
  selectedCoverFile.value = null
  coverPreview.value = ''

  // 重置表单
  bookForm.value = {
    title: '',
    author: '',
    isbn: '',
    publisher: '',
    publishDate: null,
    language: 'zh-CN',
    description: '',
    tags: []
  }

  ElMessage.success('已重置，可以开始新的导入')
}

// 跳转到图书列表
const goToLibrary = () => {
  router.push('/library')
}

// 返回上一页
const goBack = () => {
  router.go(-1)
}
</script>

<style scoped>
.enhanced-import-view {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.import-header {
  text-align: center;
  margin-bottom: 32px;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 8px 0;
}

.page-description {
  font-size: 16px;
  color: var(--el-text-color-regular);
  margin: 0;
}

.import-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.import-steps {
  margin-bottom: 32px;
}

.step-content {
  margin-bottom: 24px;
}

/* 文件选择样式 */
.file-selector {
  padding: 16px;
}

.upload-area {
  width: 100%;
  min-height: 200px;
  border: 2px dashed var(--el-border-color);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: var(--el-bg-color-page);
}

.upload-area:hover {
  border-color: var(--el-color-primary);
  background: var(--el-color-primary-light-9);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.upload-icon {
  font-size: 48px;
  color: var(--el-color-primary);
  margin-bottom: 16px;
}

.upload-text {
  text-align: center;
}

.upload-text p {
  margin: 8px 0;
  font-size: 16px;
  color: var(--el-text-color-primary);
}

.upload-hint {
  font-size: 14px !important;
  color: var(--el-text-color-regular) !important;
}

.selected-file {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 16px;
  padding: 12px;
  background: var(--el-fill-color-light);
  border-radius: 6px;
}

/* 表单布局样式 */
.form-layout {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 24px;
  margin-top: 24px;
}

@media (max-width: 768px) {
  .form-layout {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

.cover-section {
  position: sticky;
  top: 24px;
}

.cover-upload {
  padding: 16px;
}

.cover-uploader {
  width: 100%;
  height: 300px;
  border: 2px dashed var(--el-border-color);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  overflow: hidden;
}

.cover-uploader:hover {
  border-color: var(--el-color-primary);
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 6px;
}

.cover-placeholder {
  text-align: center;
  color: var(--el-text-color-secondary);
}

.cover-placeholder .el-icon {
  font-size: 48px;
  margin-bottom: 8px;
}

.cover-actions {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-top: 16px;
}

.info-section {
  padding: 16px;
}

/* 标签样式 */
.tag-item {
  margin-right: 8px;
  margin-bottom: 8px;
}

.tag-input {
  width: 100px;
  margin-right: 8px;
}

/* 确认页面样式 */
.confirm-layout {
  display: flex;
  gap: 24px;
  align-items: flex-start;
  padding: 16px;
}

.preview-cover {
  width: 200px;
  height: 300px;
  border-radius: 8px;
  overflow: hidden;
  background: var(--el-fill-color-light);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.no-cover {
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.preview-info {
  flex: 1;
}

.preview-info h3 {
  margin: 0 0 16px 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.preview-info p {
  margin: 8px 0;
  font-size: 16px;
  color: var(--el-text-color-regular);
}

.preview-tags {
  margin-top: 12px;
}

.preview-tag {
  margin-right: 8px;
  margin-bottom: 4px;
}

/* 进度和结果样式 */
.progress-content {
  padding: 16px;
}

.progress-text {
  margin-top: 12px;
  text-align: center;
  color: var(--el-text-color-regular);
}

.results-content {
  padding: 16px;
}

.result-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.result-text {
  font-size: 14px;
}

/* 操作按钮样式 */
.step-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 32px;
}
</style>
