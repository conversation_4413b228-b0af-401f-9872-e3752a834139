<template>
  <el-dialog
    v-model="dialogVisible"
    title="编辑图书信息"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
    class="book-edit-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      label-position="left"
      @keyup.ctrl.s.prevent="handleSave"
      @keyup.escape.prevent="handleClose"
    >
      <!-- 基本信息 -->
      <el-divider content-position="left">基本信息</el-divider>
      
      <el-form-item label="图书标题" prop="title">
        <el-input
          v-model="formData.title"
          placeholder="请输入图书标题"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="作者" prop="author">
        <el-input
          v-model="formData.author"
          placeholder="请输入作者姓名"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="ISBN" prop="isbn">
        <el-input
          v-model="formData.isbn"
          placeholder="请输入ISBN号码"
          maxlength="20"
        />
      </el-form-item>

      <el-form-item label="出版社" prop="publisher">
        <el-input
          v-model="formData.publisher"
          placeholder="请输入出版社名称"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="出版日期" prop="publishDate">
        <el-date-picker
          v-model="formData.publishDate"
          type="date"
          placeholder="请选择出版日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="语言" prop="language">
        <el-select
          v-model="formData.language"
          placeholder="请选择语言"
          style="width: 100%"
        >
          <el-option label="中文" value="zh" />
          <el-option label="英文" value="en" />
          <el-option label="日文" value="ja" />
          <el-option label="韩文" value="ko" />
          <el-option label="法文" value="fr" />
          <el-option label="德文" value="de" />
          <el-option label="西班牙文" value="es" />
          <el-option label="俄文" value="ru" />
          <el-option label="其他" value="other" />
        </el-select>
      </el-form-item>

      <!-- 详细信息 -->
      <el-divider content-position="left">详细信息</el-divider>

      <el-form-item label="描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="4"
          placeholder="请输入图书描述"
          maxlength="1000"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="标签" prop="tags">
        <el-select
          v-model="formData.tags"
          multiple
          filterable
          allow-create
          default-first-option
          placeholder="请输入或选择标签"
          style="width: 100%"
        >
          <el-option
            v-for="tag in commonTags"
            :key="tag"
            :label="tag"
            :value="tag"
          />
        </el-select>
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="总页数" prop="totalPages">
            <el-input-number
              v-model="formData.totalPages"
              :min="0"
              :max="99999"
              placeholder="总页数"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="字数" prop="wordCount">
            <el-input-number
              v-model="formData.wordCount"
              :min="0"
              :max="9999999"
              placeholder="字数"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 文件信息 -->
      <el-divider content-position="left">文件信息</el-divider>

      <el-form-item label="图书文件" prop="filePath">
        <div class="file-input-group">
          <el-input
            v-model="formData.filePath"
            placeholder="图书文件路径"
            readonly
            class="file-path-input"
          >
            <template #append>
              <el-button
                @click="selectBookFile"
                :loading="selectingFile"
                type="primary"
              >
                选择文件
              </el-button>
            </template>
          </el-input>
        </div>
        <div v-if="fileInfo" class="file-info">
          <span class="file-info-item">
            <el-icon><Document /></el-icon>
            {{ fileInfo.name }}
          </span>
          <span class="file-info-item">
            <el-icon><Files /></el-icon>
            {{ formatFileSize(fileInfo.size) }}
          </span>
          <span class="file-info-item">
            <el-icon><Collection /></el-icon>
            {{ fileInfo.format.toUpperCase() }}
          </span>
        </div>
      </el-form-item>

      <el-form-item label="封面图片" prop="coverPath">
        <div class="cover-input-group">
          <el-input
            v-model="formData.coverPath"
            placeholder="封面图片路径"
            readonly
            class="cover-path-input"
          >
            <template #append>
              <el-button
                @click="selectCoverFile"
                :loading="selectingCover"
                type="primary"
              >
                选择图片
              </el-button>
            </template>
          </el-input>
        </div>

        <!-- 封面预览 -->
        <div v-if="coverPreview" class="cover-preview">
          <div class="cover-image-container">
            <img
              :src="coverPreview"
              alt="封面预览"
              class="cover-image"
              @error="handleCoverError"
            />
            <div class="cover-actions">
              <el-button
                size="small"
                type="danger"
                @click="removeCover"
                :icon="Delete"
              >
                移除封面
              </el-button>
            </div>
          </div>
        </div>

        <!-- 封面信息 -->
        <div v-if="coverInfo" class="cover-info">
          <span class="cover-info-item">
            <el-icon><Picture /></el-icon>
            {{ coverInfo.name }}
          </span>
          <span class="cover-info-item">
            <el-icon><Files /></el-icon>
            {{ formatFileSize(coverInfo.size) }}
          </span>
          <span class="cover-info-item">
            <el-icon><View /></el-icon>
            {{ coverInfo.type.toUpperCase() }}
          </span>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" :disabled="saving">
          取消
        </el-button>
        <el-button
          type="primary"
          @click="handleSave"
          :loading="saving"
        >
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Document, Files, Collection, Picture, View, Delete } from '@element-plus/icons-vue'
import type { BookInfo } from '@shared/types'

// 组件属性
interface Props {
  modelValue: boolean
  bookInfo: BookInfo | null
}

// 组件事件
interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'save', bookData: Partial<BookInfo>): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const dialogVisible = ref(false)
const formRef = ref<FormInstance>()
const saving = ref(false)
const hasChanges = ref(false)

// 表单数据
const formData = reactive({
  title: '',
  author: '',
  isbn: '',
  publisher: '',
  publishDate: '',
  language: 'zh',
  description: '',
  tags: [] as string[],
  totalPages: 0,
  wordCount: 0,
  filePath: '',
  coverPath: ''
})

// 原始数据（用于检测变更）
const originalData = ref<typeof formData | null>(null)

// 文件和封面相关状态
const selectingFile = ref(false)
const selectingCover = ref(false)
const fileInfo = ref<{
  name: string
  size: number
  format: string
} | null>(null)
const coverInfo = ref<{
  name: string
  size: number
  type: string
} | null>(null)
const coverPreview = ref('')

// 常用标签
const commonTags = [
  '小说', '散文', '诗歌', '传记', '历史', '哲学', '科学', '技术',
  '编程', '设计', '艺术', '音乐', '电影', '旅行', '美食', '健康',
  '心理学', '经济学', '管理', '教育', '儿童', '青春', '言情', '悬疑',
  '科幻', '奇幻', '武侠', '推理', '恐怖', '励志', '成长', '职场'
]

// 表单验证规则
const formRules: FormRules = {
  title: [
    { required: true, message: '请输入图书标题', trigger: 'blur' },
    { min: 1, max: 200, message: '标题长度应在1-200个字符之间', trigger: 'blur' }
  ],
  author: [
    { max: 100, message: '作者姓名不能超过100个字符', trigger: 'blur' }
  ],
  isbn: [
    { max: 20, message: 'ISBN不能超过20个字符', trigger: 'blur' },
    { 
      pattern: /^[\d\-xX]*$/, 
      message: 'ISBN只能包含数字、连字符和字母X', 
      trigger: 'blur' 
    }
  ],
  publisher: [
    { max: 100, message: '出版社名称不能超过100个字符', trigger: 'blur' }
  ],
  language: [
    { required: true, message: '请选择语言', trigger: 'change' }
  ],
  description: [
    { max: 1000, message: '描述不能超过1000个字符', trigger: 'blur' }
  ],
  totalPages: [
    { type: 'number', min: 0, max: 99999, message: '页数应在0-99999之间', trigger: 'blur' }
  ],
  wordCount: [
    { type: 'number', min: 0, max: 9999999, message: '字数应在0-9999999之间', trigger: 'blur' }
  ],
  filePath: [
    { required: true, message: '请选择图书文件', trigger: 'blur' }
  ],
  coverPath: [
    // 封面路径不是必填项
  ]
}

// 监听对话框显示状态
watch(() => props.modelValue, async (newVal) => {
  dialogVisible.value = newVal
  if (newVal && props.bookInfo) {
    await initFormData()
  }
})

watch(dialogVisible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 监听表单数据变化
watch(formData, () => {
  checkForChanges()
}, { deep: true })

/**
 * 初始化表单数据
 */
const initFormData = async () => {
  if (!props.bookInfo) return

  const book = props.bookInfo
  formData.title = book.title || ''
  formData.author = book.author || ''
  formData.isbn = book.isbn || ''
  formData.publisher = book.publisher || ''
  formData.publishDate = book.publishDate || ''
  formData.language = book.language || 'zh'
  formData.description = book.description || ''
  formData.tags = Array.isArray(book.tags) ? [...book.tags] : []
  formData.totalPages = book.totalPages || 0
  formData.wordCount = book.wordCount || 0
  formData.filePath = book.filePath || ''
  formData.coverPath = book.coverPath || ''

  // 初始化文件信息
  if (formData.filePath) {
    await loadFileInfo(formData.filePath)
  }

  // 初始化封面预览
  if (formData.coverPath) {
    await loadCoverPreview(formData.coverPath)
  }

  // 保存原始数据
  originalData.value = {
    ...formData,
    tags: [...formData.tags]
  }
  hasChanges.value = false

  // 清除表单验证状态
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

/**
 * 检测数据是否有变更
 */
const checkForChanges = () => {
  if (!originalData.value) {
    hasChanges.value = false
    return
  }

  const current = formData
  const original = originalData.value

  hasChanges.value = (
    current.title !== original.title ||
    current.author !== original.author ||
    current.isbn !== original.isbn ||
    current.publisher !== original.publisher ||
    current.publishDate !== original.publishDate ||
    current.language !== original.language ||
    current.description !== original.description ||
    current.totalPages !== original.totalPages ||
    current.wordCount !== original.wordCount ||
    current.filePath !== original.filePath ||
    current.coverPath !== original.coverPath ||
    JSON.stringify(current.tags.sort()) !== JSON.stringify(original.tags.sort())
  )
}

/**
 * 处理保存操作
 */
const handleSave = async () => {
  if (!formRef.value) return

  try {
    // 表单验证
    await formRef.value.validate()

    saving.value = true

    // 准备保存数据
    const saveData: Partial<BookInfo> = {
      title: formData.title.trim(),
      author: formData.author.trim() || null,
      isbn: formData.isbn.trim() || null,
      publisher: formData.publisher.trim() || null,
      publishDate: formData.publishDate || null,
      language: formData.language,
      description: formData.description.trim() || null,
      tags: formData.tags.filter(tag => tag.trim()),
      totalPages: formData.totalPages,
      wordCount: formData.wordCount,
      filePath: formData.filePath.trim(),
      coverPath: formData.coverPath.trim() || null
    }

    // 触发保存事件
    emit('save', saveData)

  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单输入')
  } finally {
    saving.value = false
  }
}

/**
 * 处理关闭操作
 */
const handleClose = async () => {
  if (hasChanges.value) {
    try {
      await ElMessageBox.confirm(
        '您有未保存的更改，确定要关闭吗？',
        '确认关闭',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
    } catch {
      return // 用户取消关闭
    }
  }

  dialogVisible.value = false
  hasChanges.value = false
}

/**
 * 选择图书文件
 */
const selectBookFile = async () => {
  try {
    selectingFile.value = true

    // 定义支持的图书文件格式
    const bookFilters = [
      { name: '电子书文件', extensions: ['epub', 'pdf', 'txt', 'mobi', 'azw3'] },
      { name: 'EPUB', extensions: ['epub'] },
      { name: 'PDF', extensions: ['pdf'] },
      { name: '文本文件', extensions: ['txt'] },
      { name: 'MOBI', extensions: ['mobi'] },
      { name: 'AZW3', extensions: ['azw3'] },
      { name: '所有文件', extensions: ['*'] }
    ]

    const filePaths = await window.electronAPI.file.select(bookFilters)

    if (filePaths && filePaths.length > 0) {
      const selectedPath = filePaths[0]

      // 验证文件是否存在
      const exists = await window.electronAPI.file.exists(selectedPath)
      if (!exists) {
        ElMessage.error('选择的文件不存在')
        return
      }

      formData.filePath = selectedPath
      await loadFileInfo(selectedPath)

      ElMessage.success('图书文件选择成功')
    }
  } catch (error) {
    console.error('选择图书文件失败:', error)
    ElMessage.error('选择图书文件失败')
  } finally {
    selectingFile.value = false
  }
}

/**
 * 选择封面图片
 */
const selectCoverFile = async () => {
  try {
    selectingCover.value = true

    // 定义支持的图片文件格式
    const imageFilters = [
      { name: '图片文件', extensions: ['jpg', 'jpeg', 'png', 'gif', 'webp'] },
      { name: 'JPEG', extensions: ['jpg', 'jpeg'] },
      { name: 'PNG', extensions: ['png'] },
      { name: 'GIF', extensions: ['gif'] },
      { name: 'WebP', extensions: ['webp'] },
      { name: '所有文件', extensions: ['*'] }
    ]

    const filePaths = await window.electronAPI.file.select(imageFilters)

    if (filePaths && filePaths.length > 0) {
      const selectedPath = filePaths[0]

      // 验证文件是否存在
      const exists = await window.electronAPI.file.exists(selectedPath)
      if (!exists) {
        ElMessage.error('选择的图片文件不存在')
        return
      }

      formData.coverPath = selectedPath
      await loadCoverPreview(selectedPath)

      ElMessage.success('封面图片选择成功')
    }
  } catch (error) {
    console.error('选择封面图片失败:', error)
    ElMessage.error('选择封面图片失败')
  } finally {
    selectingCover.value = false
  }
}

/**
 * 加载文件信息
 */
const loadFileInfo = async (filePath: string) => {
  try {
    if (!filePath) {
      fileInfo.value = null
      return
    }

    // 调用后端API获取详细的文件信息
    const info = await window.electronAPI.file.getInfo(filePath)
    fileInfo.value = {
      name: info.name,
      size: info.size,
      format: info.format
    }
  } catch (error) {
    console.error('加载文件信息失败:', error)
    // 如果API调用失败，使用基本信息
    const fileName = filePath.split(/[/\\]/).pop() || ''
    const fileExt = fileName.split('.').pop()?.toLowerCase() || ''

    fileInfo.value = {
      name: fileName,
      size: 0,
      format: fileExt
    }
  }
}

/**
 * 加载封面预览
 */
const loadCoverPreview = async (coverPath: string) => {
  try {
    if (!coverPath) {
      coverPreview.value = ''
      coverInfo.value = null
      return
    }

    // 验证图片文件
    const validation = await window.electronAPI.file.validateImage(coverPath)
    if (!validation.isValid) {
      ElMessage.error(validation.error || '无效的图片文件')
      return
    }

    // 从文件路径提取文件名和扩展名
    const fileName = coverPath.split(/[/\\]/).pop() || ''
    const fileExt = fileName.split('.').pop()?.toLowerCase() || ''

    // 读取图片文件为base64
    const base64Data = await window.electronAPI.file.readAsBase64(coverPath)
    coverPreview.value = `data:image/${fileExt};base64,${base64Data}`

    // 设置封面信息
    coverInfo.value = {
      name: fileName,
      size: validation.info?.size || 0,
      type: validation.info?.type || fileExt
    }
  } catch (error) {
    console.error('加载封面预览失败:', error)
    coverPreview.value = ''
    coverInfo.value = null
    ElMessage.error('无法加载封面预览')
  }
}

/**
 * 移除封面
 */
const removeCover = () => {
  formData.coverPath = ''
  coverPreview.value = ''
  coverInfo.value = null
  ElMessage.success('封面已移除')
}

/**
 * 处理封面加载错误
 */
const handleCoverError = () => {
  coverPreview.value = ''
  ElMessage.error('封面图片加载失败')
}

/**
 * 格式化文件大小
 */
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '未知大小'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 暴露方法给父组件
defineExpose({
  resetForm: () => {
    formRef.value?.resetFields()
    hasChanges.value = false
  }
})
</script>

<style scoped>
.book-edit-dialog :deep(.el-dialog__body) {
  padding: 20px 20px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.el-divider {
  margin: 20px 0 16px;
}

.el-divider:first-child {
  margin-top: 0;
}

/* 表单样式优化 */
.el-form-item {
  margin-bottom: 18px;
}

.el-input-number {
  width: 100%;
}

/* 文件输入组样式 */
.file-input-group,
.cover-input-group {
  width: 100%;
}

.file-path-input,
.cover-path-input {
  width: 100%;
}

.file-info,
.cover-info {
  display: flex;
  gap: 16px;
  margin-top: 8px;
  padding: 8px 12px;
  background-color: var(--el-fill-color-lighter);
  border-radius: 4px;
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.file-info-item,
.cover-info-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.file-info-item .el-icon,
.cover-info-item .el-icon {
  font-size: 14px;
}

/* 封面预览样式 */
.cover-preview {
  margin-top: 12px;
}

.cover-image-container {
  position: relative;
  display: inline-block;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.cover-image {
  max-width: 200px;
  max-height: 300px;
  width: auto;
  height: auto;
  display: block;
  border-radius: 8px;
}

.cover-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.cover-image-container:hover .cover-actions {
  opacity: 1;
}

.cover-actions .el-button {
  backdrop-filter: blur(4px);
  background-color: rgba(255, 255, 255, 0.9);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .book-edit-dialog {
    width: 95% !important;
    margin: 5vh auto !important;
  }

  .book-edit-dialog :deep(.el-dialog__body) {
    padding: 15px 15px 0;
  }

  .el-form {
    --el-form-label-width: 80px;
  }

  .file-info,
  .cover-info {
    flex-direction: column;
    gap: 8px;
  }

  .cover-image {
    max-width: 150px;
    max-height: 225px;
  }

  .cover-actions {
    opacity: 1;
  }
}
</style>
