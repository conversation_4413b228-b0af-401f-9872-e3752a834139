/**
 * 简化EPUB解析器
 * 使用ZIP解析直接读取EPUB文件，避免第三方库兼容性问题
 */

import AdmZip from 'adm-zip'
import { parseStringPromise } from 'xml2js'
import { readFileSync } from 'fs'
import { join, dirname } from 'path'
import type {
  EpubBookInfo,
  EpubChapter,
  EpubTocItem,
  EpubParseResult,
  EpubReaderConfig,
  EpubError
} from './types'

/**
 * 解析器配置
 */
export interface EpubParserConfig {
  /** 启用图片提取 */
  enableImageExtraction?: boolean
  /** 启用样式提取 */
  enableStyleExtraction?: boolean
  /** 最大内容长度 */
  maxContentLength?: number
  /** 提取完整内容 */
  extractFullContent?: boolean
  /** 启用CFI */
  enableCFI?: boolean
  /** 超时时间（毫秒） */
  timeout?: number
}

/**
 * EPUB容器信息
 */
interface EpubContainer {
  container: {
    rootfiles: Array<{
      rootfile: Array<{
        $: {
          'full-path': string
          'media-type': string
        }
      }>
    }>
  }
}

/**
 * EPUB包信息
 */
interface EpubPackage {
  package: {
    metadata: Array<{
      'dc:title'?: string[]
      'dc:creator'?: string[]
      'dc:publisher'?: string[]
      'dc:language'?: string[]
      'dc:identifier'?: string[]
      'dc:description'?: string[]
      'dc:date'?: string[]
      'dc:rights'?: string[]
    }>
    manifest: Array<{
      item: Array<{
        $: {
          id: string
          href: string
          'media-type': string
        }
      }>
    }>
    spine: Array<{
      itemref: Array<{
        $: {
          idref: string
        }
      }>
    }>
  }
}

/**
 * 简化EPUB解析器类
 */
export class EpubParser {
  private zip: AdmZip | null = null
  private config: Required<EpubParserConfig>
  private isLoaded = false
  private filePath = ''
  private opfPath = ''
  private opfDir = ''
  private chapters: EpubChapter[] = []

  constructor(config: EpubParserConfig = {}) {
    this.config = {
      enableImageExtraction: true,
      enableStyleExtraction: true,
      maxContentLength: 50000,
      extractFullContent: false,
      enableCFI: true,
      timeout: 30000, // 30秒超时
      ...config
    }
  }

  /**
   * 快速解析EPUB文件（只解析基本信息，不加载章节内容）
   * @param filePath EPUB文件路径
   * @returns 解析结果
   */
  async parseEpubBasic(filePath: string): Promise<EpubParseResult> {
    try {
      console.log(`EpubParser: 开始简化解析EPUB文件 ${filePath}`)
      this.filePath = filePath

      // 创建超时Promise
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('解析超时')), this.config.timeout)
      })

      // 执行简化解析，带超时控制
      const parsePromise = this.performSimpleParse(filePath)
      const result = await Promise.race([parsePromise, timeoutPromise])

      console.log(`EpubParser: 简化解析完成，书名: ${result.bookInfo.title}`)
      return result

    } catch (error) {
      console.error('EpubParser: 简化解析失败:', error)
      throw new Error(`解析EPUB文件失败: ${error.message}`)
    }
  }

  /**
   * 完整解析EPUB文件（包含所有章节内容）
   * @param filePath EPUB文件路径
   * @returns 解析结果
   */
  async parseEpub(filePath: string): Promise<EpubParseResult> {
    // 暂时使用简化解析
    return this.parseEpubBasic(filePath)
  }

  /**
   * 执行简化解析工作
   */
  private async performSimpleParse(filePath: string): Promise<EpubParseResult> {
    console.log(`EpubParser: 开始ZIP解析`)

    // 读取EPUB文件作为ZIP
    this.zip = new AdmZip(filePath)
    this.filePath = filePath

    console.log(`EpubParser: 解析容器信息`)
    // 读取容器信息
    const containerXml = this.zip.readAsText('META-INF/container.xml')
    const container = await parseStringPromise(containerXml) as EpubContainer

    // 获取OPF文件路径
    this.opfPath = container.container.rootfiles[0].rootfile[0].$['full-path']
    this.opfDir = dirname(this.opfPath)

    console.log(`EpubParser: OPF文件路径: ${this.opfPath}`)

    // 读取OPF文件
    const opfXml = this.zip.readAsText(this.opfPath)
    const opfData = await parseStringPromise(opfXml) as EpubPackage

    console.log(`EpubParser: 提取元数据`)
    // 提取基本信息
    const metadata = opfData.package.metadata[0]
    const bookInfo: EpubBookInfo = {
      title: this.getMetadataValue(metadata, 'dc:title') || '未知标题',
      author: this.getMetadataValue(metadata, 'dc:creator') || '未知作者',
      publisher: this.getMetadataValue(metadata, 'dc:publisher') || '未知出版社',
      language: this.getMetadataValue(metadata, 'dc:language') || 'zh',
      identifier: this.getMetadataValue(metadata, 'dc:identifier') || '',
      description: this.getMetadataValue(metadata, 'dc:description') || '',
      publishDate: this.getMetadataValue(metadata, 'dc:date') || '',
      rights: this.getMetadataValue(metadata, 'dc:rights') || '',
      totalChapters: opfData.package.spine[0].itemref.length,
      totalWords: 0,
      estimatedReadingTime: Math.ceil(opfData.package.spine[0].itemref.length * 10),
      lastModified: new Date(),
      isFullyLoaded: false
    }

    console.log(`EpubParser: 创建章节列表`)
    // 创建简化的章节列表
    this.chapters = opfData.package.spine[0].itemref.map((itemref, index) => {
      const manifestItem = opfData.package.manifest[0].item.find(item => item.$.id === itemref.$.idref)
      return {
        id: itemref.$.idref,
        title: `第${index + 1}章`,
        content: '', // 空内容，按需加载
        order: index,
        level: 1,
        wordCount: 0,
        href: manifestItem?.$.href || '',
        mediaType: manifestItem?.$['media-type'] || 'application/xhtml+xml',
        hasImages: false,
        images: [],
        styles: []
      }
    })

    // 创建简化的目录
    const toc: EpubTocItem[] = [{
      id: 'root',
      title: bookInfo.title,
      href: '',
      level: 0,
      children: this.chapters.map((chapter, index) => ({
        id: chapter.id,
        title: chapter.title,
        href: chapter.href,
        level: 1,
        chapterIndex: index, // 添加章节索引
        children: []
      }))
    }]

    this.isLoaded = true
    console.log(`EpubParser: 简化解析完成，章节数: ${this.chapters.length}`)

    // 保持ZIP文件引用以便后续加载章节内容
    // this.zip 保持不变，不要设置为null

    return {
      bookInfo,
      chapters: this.chapters,
      toc,
      navigation: {
        landmarks: [],
        pageList: []
      }
    }
  }

  /**
   * 获取元数据值
   */
  private getMetadataValue(metadata: any, key: string): string {
    const value = metadata[key]
    if (Array.isArray(value) && value.length > 0) {
      return typeof value[0] === 'string' ? value[0] : value[0]._ || ''
    }
    return ''
  }

  /**
   * 按需加载章节内容
   */
  async loadChapterContent(chapterId: string): Promise<string> {
    if (!this.zip) {
      throw new Error('EPUB文件未加载')
    }

    try {
      // 找到章节文件路径
      const chapter = await this.findChapterById(chapterId)
      if (!chapter) {
        throw new Error(`章节 ${chapterId} 未找到`)
      }

      // 构建完整路径 - 使用正斜杠确保ZIP文件路径正确
      const fullPath = this.opfDir ? `${this.opfDir}/${chapter.href}` : chapter.href
      console.log(`EpubParser: 加载章节内容 ${chapterId}, 路径: ${fullPath}`)

      // 读取章节内容
      const rawContent = this.zip.readAsText(fullPath)
      console.log(`EpubParser: 原始内容长度: ${rawContent.length}`)

      if (rawContent.length === 0) {
        console.warn(`EpubParser: 章节文件为空或路径错误: ${fullPath}`)
        // 列出ZIP中的相关文件以便调试
        const entries = this.zip.getEntries()
        console.log('ZIP文件中的XHTML文件:')
        entries.forEach(entry => {
          if (entry.entryName.includes('.xhtml') || entry.entryName.includes('.html')) {
            console.log(`  - ${entry.entryName}`)
          }
        })
        return '章节内容为空'
      }

      // HTML内容提取
      const htmlContent = this.extractTextFromHtml(rawContent, chapterId)
      console.log(`EpubParser: 提取后内容长度: ${htmlContent.length}`)

      // 更新章节内容
      chapter.content = htmlContent
      chapter.wordCount = htmlContent.length

      console.log(`EpubParser: 章节内容加载完成，字数: ${chapter.wordCount}`)
      return htmlContent

    } catch (error) {
      console.error(`EpubParser: 加载章节内容失败:`, error)
      console.error(`EpubParser: 章节ID: ${chapterId}`)
      console.error(`EpubParser: 章节href: ${chapter?.href}`)
      console.error(`EpubParser: OPF目录: ${this.opfDir}`)
      throw error
    }
  }

  /**
   * 从HTML中提取文本内容，保留基本格式和结构
   */
  private extractTextFromHtml(htmlContent: string, chapterId?: string): string {
    try {
      console.log(`EpubParser: 开始提取HTML内容，原始长度: ${htmlContent.length}`)

      let text = htmlContent
        // 移除XML声明和DOCTYPE
        .replace(/<\?xml[^>]*\?>/gi, '')
        .replace(/<!DOCTYPE[^>]*>/gi, '')
        // 移除head标签及其内容（避免title重复）
        .replace(/<head[^>]*>[\s\S]*?<\/head>/gi, '')
        // 移除脚本和样式
        .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '')
        .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '')

        // 处理标题标签，保留HTML格式
        .replace(/<h1[^>]*>(.*?)<\/h1>/gi, '\n\n<h1>$1</h1>\n\n')
        .replace(/<h2[^>]*>(.*?)<\/h2>/gi, '\n\n<h2>$1</h2>\n\n')
        .replace(/<h3[^>]*>(.*?)<\/h3>/gi, '\n\n<h3>$1</h3>\n\n')
        .replace(/<h4[^>]*>(.*?)<\/h4>/gi, '\n\n<h4>$1</h4>\n\n')
        .replace(/<h5[^>]*>(.*?)<\/h5>/gi, '\n\n<h5>$1</h5>\n\n')
        .replace(/<h6[^>]*>(.*?)<\/h6>/gi, '\n\n<h6>$1</h6>\n\n')

        // 处理段落标签 - 保留HTML格式
        .replace(/<p[^>]*>/gi, '\n\n<p>')
        .replace(/<\/p>/gi, '</p>\n\n')

        // 处理引用块 - 保留HTML格式
        .replace(/<blockquote[^>]*>/gi, '\n\n<blockquote>')
        .replace(/<\/blockquote>/gi, '</blockquote>\n\n')

        // 处理div标签（保留内容，添加换行）
        .replace(/<div[^>]*>/gi, '\n<div>')
        .replace(/<\/div>/gi, '</div>\n')

        // 处理列表 - 保留HTML格式
        .replace(/<ul[^>]*>/gi, '\n<ul>')
        .replace(/<\/ul>/gi, '</ul>\n')
        .replace(/<ol[^>]*>/gi, '\n<ol>')
        .replace(/<\/ol>/gi, '</ol>\n')
        .replace(/<li[^>]*>/gi, '\n<li>')
        .replace(/<\/li>/gi, '</li>')

        // 处理换行标签 - 保留HTML格式
        .replace(/<br\s*\/?>/gi, '<br>')

        // 处理强调标签 - 保留HTML格式
        .replace(/<strong[^>]*>(.*?)<\/strong>/gi, '<strong>$1</strong>')
        .replace(/<b[^>]*>(.*?)<\/b>/gi, '<strong>$1</strong>')
        .replace(/<em[^>]*>(.*?)<\/em>/gi, '<em>$1</em>')
        .replace(/<i[^>]*>(.*?)<\/i>/gi, '<em>$1</em>')

        // 处理图片标签 - 转换相对路径为绝对路径
        .replace(/<img([^>]*?)src=["']([^"']+)["']([^>]*?)>/gi, (match, before, src, after) => {
          // 处理相对路径，需要传入当前章节的路径信息
          const absoluteSrc = this.resolveImagePath(src, chapterId)
          return `<img${before}src="${absoluteSrc}"${after}>`
        })

        // 移除不需要的HTML标签，保留基本格式标签（注意：移除了img，现在保留）
        .replace(/<\/?(?:html|body|head|title|meta|link|script|style|span|font|a)[^>]*>/gi, '')

        // 处理HTML实体
        .replace(/&nbsp;/g, ' ')
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&amp;/g, '&')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'")
        .replace(/&mdash;/g, '—')
        .replace(/&ndash;/g, '–')
        .replace(/&hellip;/g, '…')
        .replace(/&copy;/g, '©')
        .replace(/&reg;/g, '®')

        // 清理多余的空白，但保留格式结构
        .replace(/\n{4,}/g, '\n\n\n') // 限制最多三个连续换行
        .replace(/[ \t]+/g, ' ') // 合并空格和制表符
        .replace(/^[ \t]+|[ \t]+$/gm, '') // 只去除每行首尾的空格和制表符，保留换行
        .replace(/^\n+/, '') // 去除开头的换行
        .replace(/\n+$/, '') // 去除结尾的换行
        .trim()

      console.log(`EpubParser: HTML内容提取完成，提取后长度: ${text.length}`)

      return text || '内容为空'
    } catch (error) {
      console.error('EpubParser: HTML内容提取失败:', error)
      return htmlContent // 返回原始内容
    }
  }

  /**
   * 根据ID查找章节
   */
  private async findChapterById(chapterId: string): Promise<EpubChapter | null> {
    return this.chapters.find(chapter => chapter.id === chapterId) || null
  }

  /**
   * 解析图片路径，将相对路径转换为可用的路径
   */
  private resolveImagePath(src: string, chapterId?: string): string {
    try {
      // 如果已经是绝对路径或data URL，直接返回
      if (src.startsWith('http') || src.startsWith('data:') || src.startsWith('/')) {
        return src
      }

      // 标准化路径分隔符，统一使用正斜杠
      const normalizedSrc = src.replace(/\\/g, '/')

      // 获取当前章节的文件路径，用于正确解析相对路径
      let currentChapterDir = this.opfDir || ''
      if (chapterId) {
        const chapter = this.chapters.find(ch => ch.id === chapterId)
        if (chapter && chapter.href) {
          // 获取章节文件所在的目录
          const chapterPath = this.opfDir ? `${this.opfDir}/${chapter.href}` : chapter.href
          const lastSlashIndex = chapterPath.lastIndexOf('/')
          if (lastSlashIndex >= 0) {
            currentChapterDir = chapterPath.substring(0, lastSlashIndex)
          }
        }
      }

      console.log(`EpubParser: 解析图片路径 - src: ${normalizedSrc}, chapterId: ${chapterId}, currentChapterDir: ${currentChapterDir}`)

      // 使用改进的路径解析算法
      const imagePath = this.resolvePath(currentChapterDir, normalizedSrc)
      console.log(`EpubParser: 解析结果 - imagePath: ${imagePath}`)

      // 尝试查找并转换图片
      return this.findAndConvertImage(imagePath, normalizedSrc)

    } catch (error) {
      console.error(`EpubParser: 图片路径解析失败: ${src}`, error)
      return src
    }
  }

  /**
   * 改进的路径解析方法，正确处理相对路径
   */
  private resolvePath(basePath: string, relativePath: string): string {
    // 标准化基础路径，移除末尾的斜杠
    const normalizedBase = basePath.replace(/\\/g, '/').replace(/\/+$/, '')
    const normalizedRelative = relativePath.replace(/\\/g, '/')

    // 分割路径
    const baseParts = normalizedBase ? normalizedBase.split('/') : []
    const relativeParts = normalizedRelative.split('/')

    // 处理相对路径
    const resultParts = [...baseParts]

    for (const part of relativeParts) {
      if (part === '..') {
        // 向上一级目录
        if (resultParts.length > 0) {
          resultParts.pop()
        }
      } else if (part === '.' || part === '') {
        // 当前目录或空部分，跳过
        continue
      } else {
        // 正常路径部分
        resultParts.push(part)
      }
    }

    return resultParts.join('/')
  }

  /**
   * 查找并转换图片，包含替代路径查找机制
   */
  private findAndConvertImage(imagePath: string, originalSrc: string): string {
    if (!this.zip) {
      console.warn(`EpubParser: ZIP实例不存在`)
      return originalSrc
    }

    // 首先尝试原始解析路径
    let entry = this.zip.getEntry(imagePath)
    let finalPath = imagePath

    // 如果找不到，尝试替代路径
    if (!entry) {
      const alternativePaths = this.generateAlternativePaths(originalSrc)
      console.log(`EpubParser: 原始路径未找到，尝试替代路径:`, alternativePaths)

      for (const altPath of alternativePaths) {
        entry = this.zip.getEntry(altPath)
        if (entry) {
          finalPath = altPath
          console.log(`EpubParser: 使用替代路径: ${altPath}`)
          break
        }
      }
    }

    if (!entry) {
      // 列出ZIP中的图片文件以便调试
      this.debugListImages(originalSrc)
      console.warn(`EpubParser: 图片文件未找到: ${imagePath}`)
      return originalSrc
    }

    // 转换图片为base64
    return this.convertImageToBase64(entry, originalSrc)
  }

  /**
   * 生成替代路径列表
   */
  private generateAlternativePaths(originalSrc: string): string[] {
    const alternatives: string[] = []
    const fileName = originalSrc.split('/').pop() || originalSrc

    // 常见的图片目录
    const commonDirs = ['images', 'img', 'pics', 'pictures', 'assets', 'media', 'image']

    // 在常见目录中查找
    for (const dir of commonDirs) {
      alternatives.push(`${dir}/${fileName}`)
      if (this.opfDir) {
        alternatives.push(`${this.opfDir}/${dir}/${fileName}`)
      }
    }

    // 直接在根目录查找
    alternatives.push(fileName)

    // 在OPF目录查找
    if (this.opfDir) {
      alternatives.push(`${this.opfDir}/${fileName}`)
    }

    return alternatives
  }

  /**
   * 转换图片为base64格式，包含数据验证
   */
  private convertImageToBase64(entry: any, originalSrc: string): string {
    try {
      const imageBuffer = entry.getData()

      // 验证图片数据
      if (!imageBuffer || imageBuffer.length === 0) {
        console.warn(`EpubParser: 图片数据为空: ${originalSrc}`)
        return originalSrc
      }

      // 检查图片大小限制（10MB）
      const maxSize = 10 * 1024 * 1024
      if (imageBuffer.length > maxSize) {
        console.warn(`EpubParser: 图片过大 (${imageBuffer.length} bytes): ${originalSrc}`)
        return originalSrc
      }

      const ext = originalSrc.split('.').pop()?.toLowerCase() || 'png'
      const mimeType = this.getMimeType(ext)

      // 验证图片数据格式
      if (!this.isValidImageBuffer(imageBuffer, ext)) {
        console.warn(`EpubParser: 无效的图片数据: ${originalSrc}`)
        return originalSrc
      }

      // 转换为base64
      const base64 = imageBuffer.toString('base64')
      if (!base64) {
        console.warn(`EpubParser: Base64转换失败: ${originalSrc}`)
        return originalSrc
      }

      const dataUrl = `data:${mimeType};base64,${base64}`
      console.log(`EpubParser: 图片转换成功 ${originalSrc} -> base64 (${imageBuffer.length} bytes)`)
      return dataUrl

    } catch (error) {
      console.error(`EpubParser: 图片处理失败 ${originalSrc}:`, error)
      return originalSrc
    }
  }

  /**
   * 获取MIME类型
   */
  private getMimeType(ext: string): string {
    const mimeTypes: Record<string, string> = {
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'gif': 'image/gif',
      'svg': 'image/svg+xml',
      'webp': 'image/webp',
      'bmp': 'image/bmp'
    }
    return mimeTypes[ext] || 'image/png'
  }

  /**
   * 验证图片数据格式
   */
  private isValidImageBuffer(buffer: Buffer, ext: string): boolean {
    if (!buffer || buffer.length < 10) return false

    // 检查文件头魔数
    const header = buffer.slice(0, 10)

    switch (ext) {
      case 'jpg':
      case 'jpeg':
        return header[0] === 0xFF && header[1] === 0xD8
      case 'png':
        return header[0] === 0x89 && header[1] === 0x50 && header[2] === 0x4E && header[3] === 0x47
      case 'gif':
        return header.toString('ascii', 0, 3) === 'GIF'
      case 'webp':
        return header.toString('ascii', 0, 4) === 'RIFF' && header.toString('ascii', 8, 12) === 'WEBP'
      default:
        return true // 对于其他格式，暂时返回true
    }
  }

  /**
   * 调试：列出ZIP中的图片文件
   */
  private debugListImages(originalSrc: string) {
    if (!this.zip) return

    console.group(`EpubParser: 调试图片路径 - ${originalSrc}`)
    console.log(`OPF目录: ${this.opfDir}`)

    const entries = this.zip.getEntries()
    const imageEntries = entries.filter(entry =>
      /\.(jpg|jpeg|png|gif|svg|webp|bmp)$/i.test(entry.entryName)
    )

    console.log('ZIP中的图片文件:')
    imageEntries.forEach(entry => {
      console.log(`  - ${entry.entryName} (${entry.header.size} bytes)`)
    })

    // 显示所有尝试的路径
    const alternatives = this.generateAlternativePaths(originalSrc)
    console.log('尝试的替代路径:')
    alternatives.forEach(path => {
      const exists = this.zip!.getEntry(path) ? '✓' : '✗'
      console.log(`  ${exists} ${path}`)
    })

    console.groupEnd()
  }

  /**
   * 获取EPUB调试信息（用于开发和调试）
   */
  getDebugInfo(): any {
    if (!this.zip) return null

    const entries = this.zip.getEntries()
    const imageEntries = entries.filter(entry =>
      /\.(jpg|jpeg|png|gif|svg|webp|bmp)$/i.test(entry.entryName)
    )

    return {
      opfPath: this.opfPath,
      opfDir: this.opfDir,
      totalEntries: entries.length,
      imageCount: imageEntries.length,
      images: imageEntries.map(entry => ({
        path: entry.entryName,
        size: entry.header.size,
        compressedSize: entry.header.compressedSize
      })),
      chapters: this.chapters.map(ch => ({
        id: ch.id,
        title: ch.title,
        href: ch.href
      }))
    }
  }

  /**
   * 清理资源
   */
  dispose(): void {
    this.zip = null
    this.isLoaded = false
    this.filePath = ''
    this.opfPath = ''
    this.opfDir = ''
  }

  /**
   * 销毁解析器（兼容旧接口）
   */
  destroy(): void {
    this.dispose()
  }
}



