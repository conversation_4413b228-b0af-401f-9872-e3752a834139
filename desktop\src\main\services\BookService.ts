/**
 * 图书服务类
 * 统一处理图书相关的业务逻辑，解决数据库字段不一致问题
 */

import { DatabaseManager } from '../database-manager'
import { FileManager } from '../file-manager'
import { randomUUID } from 'crypto'
import type { BookInfo } from '@shared/types'

export interface BookRecord {
  id: number
  title: string
  author: string
  isbn?: string
  file_path: string
  file_format: string
  file_size: number
  cover_image?: string
  description?: string
  publisher?: string
  publish_date?: string
  language: string
  total_pages: number
  word_count: number
  reading_status: 'unread' | 'reading' | 'finished'
  current_page: number
  reading_progress_percent: number
  last_read_time?: string
  is_favorite: boolean
  import_time: string
  tags?: string
  metadata_json?: string
  created_at: string
  updated_at: string
  deleted_at?: string
  sync_version: number
}

export class BookService {
  constructor(
    private databaseManager: DatabaseManager,
    private fileManager: FileManager
  ) {}

  /**
   * 获取所有图书列表
   */
  async getAllBooks(): Promise<BookInfo[]> {
    try {
      // 检查数据库连接
      if (!this.databaseManager) {
        throw new Error('数据库管理器未初始化')
      }

      const db = this.databaseManager.getDatabase()
      if (!db) {
        throw new Error('数据库连接失败')
      }

      const stmt = db.prepare(`
        SELECT
          id, title, author, isbn, file_path, file_format, file_size,
          cover_image, description, publisher, publish_date, language,
          total_pages, word_count, reading_status, current_page,
          reading_progress_percent, last_read_time, is_favorite, import_time,
          tags, metadata_json, created_at, updated_at, deleted_at, sync_version
        FROM books
        WHERE deleted_at IS NULL
        ORDER BY
          CASE WHEN last_read_time IS NOT NULL THEN last_read_time ELSE import_time END DESC
      `)

      const rows = stmt.all() as BookRecord[]
      console.log(`BookService: 成功查询到 ${rows.length} 本图书`)

      // 映射数据并处理可能的映射错误
      const books: BookInfo[] = []
      const errors: string[] = []

      rows.forEach((row, index) => {
        try {
          const bookInfo = this.mapToBookInfo(row)
          books.push(bookInfo)
        } catch (mappingError) {
          console.error(`BookService: 映射第 ${index + 1} 本图书失败:`, mappingError)
          errors.push(`图书ID ${row.id} 数据映射失败`)
        }
      })

      if (errors.length > 0) {
        console.warn(`BookService: ${errors.length} 本图书映射失败:`, errors)
      }

      return books
    } catch (error) {
      console.error('BookService: 获取图书列表失败:', error)

      // 根据错误类型提供更具体的错误信息
      if (error instanceof Error) {
        if (error.message.includes('数据库')) {
          throw new Error('数据库连接失败，请检查数据库文件是否存在')
        } else if (error.message.includes('SQLITE')) {
          throw new Error('数据库查询失败，可能是表结构问题')
        } else {
          throw new Error(`获取图书列表失败: ${error.message}`)
        }
      } else {
        throw new Error('获取图书列表时发生未知错误')
      }
    }
  }

  /**
   * 根据ID获取图书
   */
  async getBookById(id: string | number): Promise<BookInfo | null> {
    try {
      const db = this.databaseManager.getDatabase()

      // 将字符串ID转换为数字
      const numericId = typeof id === 'string' ? parseInt(id, 10) : id

      if (isNaN(numericId) || numericId <= 0) {
        throw new Error('无效的图书ID')
      }

      const stmt = db.prepare(`
        SELECT
          id, title, author, isbn, file_path, file_format, file_size,
          cover_image, description, publisher, publish_date, language,
          total_pages, word_count, reading_status, current_page,
          reading_progress_percent, last_read_time, is_favorite, import_time,
          tags, metadata_json, created_at, updated_at, deleted_at, sync_version
        FROM books
        WHERE id = ? AND deleted_at IS NULL
      `)

      const row = stmt.get(numericId) as BookRecord | undefined

      return row ? this.mapToBookInfo(row) : null
    } catch (error) {
      console.error('获取图书失败:', error)
      throw new Error('获取图书失败')
    }
  }

  /**
   * 添加图书
   */
  async addBook(filePath: string): Promise<BookInfo> {
    try {
      // 检查文件是否存在
      if (!await this.fileManager.fileExists(filePath)) {
        throw new Error('文件不存在')
      }

      // 检查是否已经导入
      const existing = await this.findBookByFilePath(filePath)
      if (existing) {
        throw new Error('该文件已经导入')
      }

      // 获取文件信息
      const fileInfo = await this.fileManager.getFileInfo(filePath)

      // 提取基本信息
      const title = this.extractTitleFromFileName(fileInfo.name)
      const author = '未知作者' // TODO: 从文件中提取作者信息

      const bookRecord: Omit<BookRecord, 'id' | 'created_at' | 'updated_at' | 'import_time'> = {
        title,
        author,
        isbn: null,
        file_path: filePath,
        file_format: fileInfo.format.toLowerCase(),
        file_size: fileInfo.size,
        cover_image: null,
        description: '',
        publisher: null,
        publish_date: null,
        language: 'zh-CN',
        total_pages: 0,
        word_count: 0,
        reading_status: 'unread',
        current_page: 0,
        reading_progress_percent: 0.0,
        last_read_time: null,
        is_favorite: false,
        tags: null,
        metadata_json: null,
        deleted_at: null,
        sync_version: 1
      }

      // 插入数据库
      const db = this.databaseManager.getDatabase()
      const stmt = db.prepare(`
        INSERT INTO books (
          title, author, isbn, file_path, file_format, file_size,
          cover_image, description, publisher, publish_date, language,
          total_pages, word_count, reading_status, current_page,
          reading_progress_percent, last_read_time, is_favorite, tags, metadata_json,
          sync_version
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `)

      const result = stmt.run(
        bookRecord.title,
        bookRecord.author,
        bookRecord.isbn,
        bookRecord.file_path,
        bookRecord.file_format,
        bookRecord.file_size,
        bookRecord.cover_image,
        bookRecord.description,
        bookRecord.publisher,
        bookRecord.publish_date,
        bookRecord.language,
        bookRecord.total_pages,
        bookRecord.word_count,
        bookRecord.reading_status,
        bookRecord.current_page,
        bookRecord.reading_progress_percent,
        bookRecord.last_read_time,
        bookRecord.is_favorite,
        bookRecord.tags,
        bookRecord.metadata_json,
        bookRecord.sync_version
      )

      // 获取插入的记录
      const insertedBook = await this.getBookById(result.lastInsertRowid as number)
      if (!insertedBook) {
        throw new Error('插入图书后无法获取记录')
      }

      return insertedBook
    } catch (error) {
      console.error('添加图书失败:', error)
      throw error
    }
  }

  /**
   * 添加图书（支持自定义信息和封面）
   */
  async addBookWithDetails(bookData: {
    filePath: string
    title: string
    author?: string
    isbn?: string
    publisher?: string
    publishDate?: string
    language?: string
    description?: string
    tags?: string[]
    coverFile?: string // 封面文件路径
  }): Promise<BookInfo> {
    try {
      // 检查文件是否存在
      if (!await this.fileManager.fileExists(bookData.filePath)) {
        throw new Error('文件不存在')
      }

      // 检查是否已经导入
      const existing = await this.findBookByFilePath(bookData.filePath)
      if (existing) {
        throw new Error('该文件已经导入')
      }

      // 获取文件信息
      const fileInfo = await this.fileManager.getFileInfo(bookData.filePath)

      // 处理封面图片
      let coverPath: string | null = null
      if (bookData.coverFile) {
        const bookId = randomUUID()
        coverPath = await this.fileManager.saveCover(bookId,
          await this.fileManager.readFile(bookData.coverFile))
      }

      const bookRecord: Omit<BookRecord, 'id' | 'created_at' | 'updated_at' | 'import_time'> = {
        title: bookData.title,
        author: bookData.author || '未知作者',
        isbn: bookData.isbn || null,
        file_path: bookData.filePath,
        file_format: fileInfo.format.toLowerCase(),
        file_size: fileInfo.size,
        cover_image: coverPath,
        description: bookData.description || '',
        publisher: bookData.publisher || null,
        publish_date: bookData.publishDate || null,
        language: bookData.language || 'zh-CN',
        total_pages: 0,
        word_count: 0,
        reading_status: 'unread',
        current_page: 0,
        reading_progress_percent: 0.0,
        last_read_time: null,
        is_favorite: false,
        tags: bookData.tags ? JSON.stringify(bookData.tags) : null,
        metadata_json: null,
        deleted_at: null,
        sync_version: 1
      }

      // 插入数据库
      const db = this.databaseManager.getDatabase()
      const stmt = db.prepare(`
        INSERT INTO books (
          title, author, isbn, file_path, file_format, file_size,
          cover_image, description, publisher, publish_date, language,
          total_pages, word_count, reading_status, current_page,
          reading_progress_percent, last_read_time, is_favorite, tags,
          metadata_json, sync_version
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `)

      const result = stmt.run(
        bookRecord.title,
        bookRecord.author,
        bookRecord.isbn,
        bookRecord.file_path,
        bookRecord.file_format,
        bookRecord.file_size,
        bookRecord.cover_image,
        bookRecord.description,
        bookRecord.publisher,
        bookRecord.publish_date,
        bookRecord.language,
        bookRecord.total_pages,
        bookRecord.word_count,
        bookRecord.reading_status,
        bookRecord.current_page,
        bookRecord.reading_progress_percent,
        bookRecord.last_read_time,
        bookRecord.is_favorite,
        bookRecord.tags,
        bookRecord.metadata_json,
        bookRecord.sync_version
      )

      // 获取插入的记录
      const insertedBook = await this.getBookById(result.lastInsertRowid as number)
      if (!insertedBook) {
        throw new Error('插入图书后无法获取记录')
      }

      return insertedBook
    } catch (error) {
      console.error('添加图书失败:', error)
      throw error
    }
  }

  /**
   * 从EPUB文件提取元数据和封面
   */
  async extractBookMetadata(filePath: string): Promise<{
    title?: string
    author?: string
    publisher?: string
    description?: string
    language?: string
    coverData?: Buffer
  }> {
    try {
      const fileInfo = await this.fileManager.getFileInfo(filePath)

      if (fileInfo.format.toLowerCase() === 'epub') {
        // 使用EPUB解析器提取信息
        const { EpubParser } = await import('@shared/services/epub/EpubParser')
        const epubParser = new EpubParser()
        const result = await epubParser.parseFile(filePath)

        return {
          title: result.bookInfo.title,
          author: result.bookInfo.author,
          publisher: result.bookInfo.publisher,
          description: result.bookInfo.description,
          language: result.bookInfo.language,
          coverData: result.bookInfo.coverImage ?
            Buffer.from(result.bookInfo.coverImage.split(',')[1], 'base64') : undefined
        }
      }

      // 对于其他格式，返回基本信息
      return {
        title: this.extractTitleFromFileName(fileInfo.name)
      }
    } catch (error) {
      console.error('提取图书元数据失败:', error)
      return {}
    }
  }

  /**
   * 删除图书（包含文件清理和关联数据清理）
   */
  async removeBook(id: string | number, options?: { deleteFiles?: boolean }): Promise<boolean> {
    try {
      const db = this.databaseManager.getDatabase()

      // 将字符串ID转换为数字
      const numericId = typeof id === 'string' ? parseInt(id, 10) : id

      if (isNaN(numericId) || numericId <= 0) {
        throw new Error('无效的图书ID')
      }

      // 首先获取图书信息，用于清理文件
      const book = await this.getBookById(numericId)
      if (!book) {
        console.log(`图书 ${numericId} 不存在或已被删除`)
        return false
      }

      console.log(`开始删除图书: ${book.title} (ID: ${numericId})`)

      // 开始事务，确保数据一致性
      const transaction = db.transaction(() => {
        // 1. 软删除图书记录
        const bookStmt = db.prepare(`
          UPDATE books
          SET deleted_at = CURRENT_TIMESTAMP
          WHERE id = ? AND deleted_at IS NULL
        `)
        const bookResult = bookStmt.run(numericId)

        if (bookResult.changes === 0) {
          throw new Error('图书不存在或已被删除')
        }

        // 2. 删除关联的书签（由于外键约束，这些会自动级联删除，但我们显式处理以确保清理）
        const bookmarkStmt = db.prepare(`
          DELETE FROM bookmarks WHERE book_id = ?
        `)
        const bookmarkResult = bookmarkStmt.run(numericId.toString())
        console.log(`删除了 ${bookmarkResult.changes} 个书签`)

        // 3. 删除关联的笔记（如果存在notes表）
        try {
          const noteStmt = db.prepare(`
            DELETE FROM notes WHERE book_id = ?
          `)
          const noteResult = noteStmt.run(numericId.toString())
          console.log(`删除了 ${noteResult.changes} 个笔记`)
        } catch (error) {
          // notes表可能不存在，忽略错误
          console.log('notes表不存在，跳过笔记删除')
        }

        // 4. 删除阅读进度记录（如果存在reading_progress表）
        try {
          const progressStmt = db.prepare(`
            DELETE FROM reading_progress WHERE book_id = ?
          `)
          const progressResult = progressStmt.run(numericId.toString())
          console.log(`删除了 ${progressResult.changes} 个阅读进度记录`)
        } catch (error) {
          // reading_progress表可能不存在，忽略错误
          console.log('reading_progress表不存在，跳过阅读进度删除')
        }

        return bookResult.changes > 0
      })

      // 执行事务
      const success = transaction()

      if (success) {
        // 5. 清理文件（在事务外执行，避免文件操作影响数据库事务）
        await this.cleanupBookFiles(book, options?.deleteFiles)
        console.log(`图书删除成功: ${book.title}`)
      }

      return success
    } catch (error) {
      console.error('删除图书失败:', error)
      throw new Error(error instanceof Error ? error.message : '删除图书失败')
    }
  }

  /**
   * 清理图书相关文件
   */
  private async cleanupBookFiles(book: BookInfo, deleteOriginalFile: boolean = false): Promise<void> {
    try {
      // 删除封面图片
      if (book.coverPath) {
        try {
          await this.fileManager.deleteCover(book.coverPath)
          console.log(`删除封面文件: ${book.coverPath}`)
        } catch (error) {
          console.warn(`删除封面文件失败: ${book.coverPath}`, error)
        }
      }

      // 可选：删除原始图书文件
      if (deleteOriginalFile && book.filePath) {
        try {
          if (await this.fileManager.fileExists(book.filePath)) {
            const fs = require('fs').promises
            await fs.unlink(book.filePath)
            console.log(`删除原始文件: ${book.filePath}`)
          }
        } catch (error) {
          console.warn(`删除原始文件失败: ${book.filePath}`, error)
        }
      }

      // 清理可能的临时文件或缓存文件
      // TODO: 根据需要添加更多文件清理逻辑

    } catch (error) {
      console.error('清理图书文件失败:', error)
      // 文件清理失败不应该影响删除操作的成功
    }
  }

  /**
   * 更新阅读进度
   */
  async updateProgress(
    id: string | number,
    progress: number,
    currentPage?: number
  ): Promise<boolean> {
    try {
      const db = this.databaseManager.getDatabase()

      // 将字符串ID转换为数字
      const numericId = typeof id === 'string' ? parseInt(id, 10) : id

      if (isNaN(numericId) || numericId <= 0) {
        throw new Error('无效的图书ID')
      }

      // 确定阅读状态
      let readingStatus: BookRecord['reading_status'] = 'reading'
      if (progress === 0) {
        readingStatus = 'unread'
      } else if (progress >= 100) {
        readingStatus = 'finished'
      }

      const stmt = db.prepare(`
        UPDATE books
        SET reading_progress_percent = ?,
            current_page = ?,
            reading_status = ?,
            last_read_time = CURRENT_TIMESTAMP
        WHERE id = ? AND deleted_at IS NULL
      `)

      const result = stmt.run(
        progress,
        currentPage || 0,
        readingStatus,
        numericId
      )

      return result.changes > 0
    } catch (error) {
      console.error('更新阅读进度失败:', error)
      throw new Error('更新阅读进度失败')
    }
  }

  /**
   * 更新图书信息
   */
  async updateBookInfo(id: string | number, bookData: Partial<BookInfo>): Promise<boolean> {
    try {
      const db = this.databaseManager.getDatabase()

      // 将字符串ID转换为数字
      const numericId = typeof id === 'string' ? parseInt(id, 10) : id

      if (isNaN(numericId) || numericId <= 0) {
        throw new Error('无效的图书ID')
      }

      console.log(`开始更新图书信息: ${numericId}`, bookData)

      // 首先检查图书是否存在
      const existingBook = await this.getBookById(numericId)
      if (!existingBook) {
        throw new Error('图书不存在')
      }

      // 构建更新字段和值
      const updateFields: string[] = []
      const updateValues: any[] = []

      // 处理各个可更新字段
      if (bookData.title !== undefined) {
        updateFields.push('title = ?')
        updateValues.push(bookData.title.trim())
      }

      if (bookData.author !== undefined) {
        updateFields.push('author = ?')
        updateValues.push(bookData.author?.trim() || null)
      }

      if (bookData.isbn !== undefined) {
        updateFields.push('isbn = ?')
        updateValues.push(bookData.isbn?.trim() || null)
      }

      if (bookData.publisher !== undefined) {
        updateFields.push('publisher = ?')
        updateValues.push(bookData.publisher?.trim() || null)
      }

      if (bookData.publishDate !== undefined) {
        updateFields.push('publish_date = ?')
        updateValues.push(bookData.publishDate || null)
      }

      if (bookData.language !== undefined) {
        updateFields.push('language = ?')
        updateValues.push(bookData.language)
      }

      if (bookData.description !== undefined) {
        updateFields.push('description = ?')
        updateValues.push(bookData.description?.trim() || null)
      }

      if (bookData.totalPages !== undefined) {
        updateFields.push('total_pages = ?')
        updateValues.push(bookData.totalPages)
      }

      if (bookData.wordCount !== undefined) {
        updateFields.push('word_count = ?')
        updateValues.push(bookData.wordCount)
      }

      if (bookData.tags !== undefined) {
        updateFields.push('tags = ?')
        updateValues.push(JSON.stringify(bookData.tags))
      }

      // 处理文件路径更新
      if (bookData.filePath !== undefined) {
        // 验证文件路径
        if (bookData.filePath.trim()) {
          const fileExists = await this.validateFilePath(bookData.filePath.trim())
          if (!fileExists) {
            throw new Error('指定的图书文件不存在')
          }
          updateFields.push('file_path = ?')
          updateValues.push(bookData.filePath.trim())
        } else {
          throw new Error('图书文件路径不能为空')
        }
      }

      // 处理封面路径更新
      if (bookData.coverPath !== undefined) {
        if (bookData.coverPath && bookData.coverPath.trim()) {
          // 验证封面文件
          const coverValid = await this.validateCoverPath(bookData.coverPath.trim())
          if (!coverValid) {
            throw new Error('指定的封面图片文件不存在或格式不支持')
          }

          // 如果有旧封面，需要清理
          if (existingBook.coverPath && existingBook.coverPath !== bookData.coverPath.trim()) {
            await this.cleanupOldCover(existingBook.coverPath)
          }

          updateFields.push('cover_image = ?')
          updateValues.push(bookData.coverPath.trim())
        } else {
          // 清空封面路径
          if (existingBook.coverPath) {
            await this.cleanupOldCover(existingBook.coverPath)
          }
          updateFields.push('cover_image = ?')
          updateValues.push(null)
        }
      }

      // 如果没有要更新的字段，直接返回成功
      if (updateFields.length === 0) {
        console.log('没有需要更新的字段')
        return true
      }

      // 添加更新时间戳
      updateFields.push('updated_at = CURRENT_TIMESTAMP')

      // 构建SQL语句
      const sql = `
        UPDATE books
        SET ${updateFields.join(', ')}
        WHERE id = ? AND deleted_at IS NULL
      `

      // 添加ID到参数列表
      updateValues.push(numericId)

      console.log('执行SQL:', sql)
      console.log('参数:', updateValues)

      const stmt = db.prepare(sql)
      const result = stmt.run(...updateValues)

      const success = result.changes > 0
      console.log(`图书信息更新${success ? '成功' : '失败'}: ${numericId}`)

      return success
    } catch (error) {
      console.error('更新图书信息失败:', error)
      throw new Error(error instanceof Error ? error.message : '更新图书信息失败')
    }
  }

  /**
   * 搜索图书
   */
  async searchBooks(query: string, limit: number = 50): Promise<BookInfo[]> {
    try {
      const db = this.databaseManager.getDatabase()
      const searchPattern = `%${query}%`
      
      const stmt = db.prepare(`
        SELECT
          id, title, author, isbn, file_path, file_format, file_size,
          cover_image, description, publisher, publish_date, language,
          total_pages, word_count, reading_status, current_page,
          reading_progress_percent, last_read_time, import_time,
          tags, metadata_json, created_at, updated_at, deleted_at, sync_version
        FROM books
        WHERE deleted_at IS NULL
          AND (title LIKE ? OR author LIKE ? OR description LIKE ? OR publisher LIKE ?)
        ORDER BY
          CASE
            WHEN title LIKE ? THEN 1
            WHEN author LIKE ? THEN 2
            WHEN publisher LIKE ? THEN 3
            ELSE 4
          END,
          title
        LIMIT ?
      `)
      
      const rows = stmt.all(
        searchPattern, searchPattern, searchPattern, searchPattern,
        searchPattern, searchPattern, searchPattern,
        limit
      ) as BookRecord[]
      
      return rows.map(row => this.mapToBookInfo(row))
    } catch (error) {
      console.error('搜索图书失败:', error)
      throw new Error('搜索图书失败')
    }
  }

  /**
   * 根据文件路径查找图书
   */
  private async findBookByFilePath(filePath: string): Promise<BookRecord | null> {
    try {
      const db = this.databaseManager.getDatabase()
      const stmt = db.prepare(`
        SELECT * FROM books 
        WHERE file_path = ? AND deleted_at IS NULL
      `)
      
      return stmt.get(filePath) as BookRecord | null
    } catch (error) {
      console.error('根据文件路径查找图书失败:', error)
      return null
    }
  }

  /**
   * 从文件名提取标题
   */
  private extractTitleFromFileName(fileName: string): string {
    // 移除文件扩展名
    const nameWithoutExt = fileName.replace(/\.[^/.]+$/, '')
    
    // 移除常见的无用字符
    return nameWithoutExt
      .replace(/[_-]/g, ' ')
      .replace(/\s+/g, ' ')
      .trim()
  }

  /**
   * 将数据库记录映射为BookInfo对象
   * 严格按照新的BookInfo接口的25个字段进行映射
   */
  private mapToBookInfo(record: BookRecord): BookInfo {
    try {
      // 验证必需字段
      if (!record.id) {
        throw new Error('图书ID不能为空')
      }
      if (!record.title || record.title.trim() === '') {
        throw new Error('图书标题不能为空')
      }
      if (!record.file_path || record.file_path.trim() === '') {
        throw new Error('图书文件路径不能为空')
      }

      // 安全地解析JSON字段
      let tags: string[] = []
      let metadata: Record<string, any> = {}

      if (record.tags) {
        try {
          tags = JSON.parse(record.tags)
          if (!Array.isArray(tags)) {
            console.warn(`图书ID ${record.id}: tags字段不是数组格式，使用默认值`)
            tags = []
          }
        } catch (error) {
          console.warn(`图书ID ${record.id}: tags字段JSON解析失败，使用默认值`)
          tags = []
        }
      }

      if (record.metadata_json) {
        try {
          metadata = JSON.parse(record.metadata_json)
          if (typeof metadata !== 'object' || metadata === null) {
            console.warn(`图书ID ${record.id}: metadata_json字段不是对象格式，使用默认值`)
            metadata = {}
          }
        } catch (error) {
          console.warn(`图书ID ${record.id}: metadata_json字段JSON解析失败，使用默认值`)
          metadata = {}
        }
      }

      // 安全地解析日期字段
      let addedAt: Date
      let lastReadAt: Date | null = null
      let createdAt: Date
      let updatedAt: Date
      let deletedAt: Date | null = null

      // 处理 import_time (addedAt)
      try {
        addedAt = new Date(record.import_time)
        if (isNaN(addedAt.getTime())) {
          throw new Error('导入时间格式无效')
        }
      } catch (error) {
        console.warn(`图书ID ${record.id}: 导入时间解析失败，使用当前时间`)
        addedAt = new Date()
      }

      // 处理 last_read_time (lastReadAt)
      if (record.last_read_time) {
        try {
          lastReadAt = new Date(record.last_read_time)
          if (isNaN(lastReadAt.getTime())) {
            lastReadAt = null
          }
        } catch (error) {
          console.warn(`图书ID ${record.id}: 最后阅读时间解析失败`)
          lastReadAt = null
        }
      }

      // 处理 created_at
      try {
        createdAt = new Date(record.created_at)
        if (isNaN(createdAt.getTime())) {
          createdAt = addedAt // 使用导入时间作为备用
        }
      } catch (error) {
        createdAt = addedAt
      }

      // 处理 updated_at
      try {
        updatedAt = new Date(record.updated_at)
        if (isNaN(updatedAt.getTime())) {
          updatedAt = createdAt // 使用创建时间作为备用
        }
      } catch (error) {
        updatedAt = createdAt
      }

      // 处理 deleted_at
      if (record.deleted_at) {
        try {
          deletedAt = new Date(record.deleted_at)
          if (isNaN(deletedAt.getTime())) {
            deletedAt = null
          }
        } catch (error) {
          deletedAt = null
        }
      }

      // 验证和处理数值字段
      const readProgress = Math.max(0, Math.min(100, record.reading_progress_percent || 0))
      const fileSize = Math.max(0, record.file_size || 0)
      const currentPage = Math.max(0, record.current_page || 0)
      const totalPages = Math.max(0, record.total_pages || 0)
      const wordCount = Math.max(0, record.word_count || 0)
      const syncVersion = Math.max(1, record.sync_version || 1)

      // 严格按照新BookInfo接口的25个字段返回
      return {
        // 基础字段
        id: record.id.toString(),
        title: record.title.trim(),
        author: record.author || null,
        isbn: record.isbn || null,
        filePath: record.file_path.trim(),
        format: (record.file_format || 'txt') as 'epub' | 'pdf' | 'txt' | 'mobi' | 'azw3',
        fileSize,
        coverPath: record.cover_image || null,
        description: record.description || null,
        publisher: record.publisher || null,
        publishDate: record.publish_date || null,
        language: record.language || 'zh-CN',
        totalPages,
        wordCount,
        readingStatus: (record.reading_status as 'unread' | 'reading' | 'finished') || 'unread',
        currentPage,
        readProgress,
        lastReadAt,
        isFavorite: Boolean(record.is_favorite),
        addedAt,
        tags,
        metadata,
        createdAt,
        updatedAt,
        deletedAt,
        syncVersion
      }
    } catch (error) {
      console.error(`BookService: 映射图书数据失败 (ID: ${record.id}):`, error)
      throw new Error(`图书数据映射失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 设置图书收藏状态
   */
  async setFavorite(id: string | number, isFavorite: boolean): Promise<boolean> {
    try {
      const db = this.databaseManager.getDatabase()

      // 将字符串ID转换为数字
      const numericId = typeof id === 'string' ? parseInt(id, 10) : id

      if (isNaN(numericId) || numericId <= 0) {
        throw new Error('无效的图书ID')
      }

      const stmt = db.prepare(`
        UPDATE books
        SET is_favorite = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ? AND deleted_at IS NULL
      `)

      const result = stmt.run(isFavorite ? 1 : 0, numericId)

      if (result.changes === 0) {
        throw new Error('图书不存在或已被删除')
      }

      console.log(`BookService: 成功更新图书收藏状态 (ID: ${numericId}, 收藏: ${isFavorite})`)
      return true

    } catch (error) {
      console.error('BookService: 设置收藏状态失败:', error)
      throw new Error(`设置收藏状态失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 验证文件路径是否有效
   */
  private async validateFilePath(filePath: string): Promise<boolean> {
    try {
      // 检查文件是否存在
      const exists = await this.fileManager.fileExists(filePath)
      if (!exists) {
        return false
      }

      // 检查文件格式是否支持
      const supportedFormats = ['.epub', '.pdf', '.txt', '.mobi', '.azw3']
      const fileExt = filePath.toLowerCase().split('.').pop()
      if (!fileExt || !supportedFormats.includes(`.${fileExt}`)) {
        return false
      }

      return true
    } catch (error) {
      console.error('验证文件路径失败:', error)
      return false
    }
  }

  /**
   * 验证封面图片路径是否有效
   */
  private async validateCoverPath(coverPath: string): Promise<boolean> {
    try {
      // 检查文件是否存在
      const exists = await this.fileManager.fileExists(coverPath)
      if (!exists) {
        return false
      }

      // 验证图片文件格式
      const validation = await this.fileManager.validateImageFile(coverPath)
      return validation.isValid
    } catch (error) {
      console.error('验证封面路径失败:', error)
      return false
    }
  }

  /**
   * 清理旧封面文件
   */
  private async cleanupOldCover(oldCoverPath: string): Promise<void> {
    try {
      console.log(`清理旧封面文件: ${oldCoverPath}`)
      await this.fileManager.deleteCover(oldCoverPath)
    } catch (error) {
      console.error('清理旧封面文件失败:', error)
      // 不抛出错误，因为清理失败不应该影响主要功能
    }
  }
}
