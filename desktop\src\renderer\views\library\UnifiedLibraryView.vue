<template>
  <div class="unified-library-view">
    <!-- 头部区域 -->
    <div class="library-header">
      <div class="header-left">
        <h1 class="page-title">图书列表</h1>
        <div class="stats-info">
          <span>共 {{ stats.total }} 本图书</span>
          <span v-if="stats.reading > 0">，正在阅读 {{ stats.reading }} 本</span>
          <span v-if="stats.finished > 0">，已完成 {{ stats.finished }} 本</span>
        </div>
      </div>
      
      <div class="header-actions">
        <el-button 
          type="primary" 
          :icon="Plus" 
          @click="handleAddBooks"
          :loading="loading"
        >
          添加图书
        </el-button>
        
        <el-button 
          :icon="Refresh" 
          @click="handleRefresh"
          :loading="loading"
        >
          刷新
        </el-button>
      </div>
    </div>



    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <!-- 搜索框 -->
        <el-input
          v-model="searchQuery"
          placeholder="搜索图书标题、作者、出版社或标签..."
          :prefix-icon="Search"
          clearable
          @input="handleSearch"
          class="search-input"
        />

        <!-- 筛选器 -->
        <el-select
          v-model="safeFilter.format"
          placeholder="全部格式"
          @change="handleFilterChange"
          style="width: 120px"
        >
          <el-option label="全部格式" value="all" />
          <el-option
            v-for="format in availableFormats"
            :key="format"
            :label="(format || 'unknown').toUpperCase()"
            :value="format"
          />
        </el-select>

        <el-select
          v-model="safeFilter.readingStatus"
          placeholder="全部状态"
          @change="handleFilterChange"
          style="width: 120px"
        >
          <el-option label="全部状态" value="all" />
          <el-option label="未读" value="unread" />
          <el-option label="阅读中" value="reading" />
          <el-option label="已完成" value="finished" />
        </el-select>

        <el-select
          v-model="safeFilter.author"
          placeholder="全部作者"
          @change="handleFilterChange"
          style="width: 120px"
          filterable
        >
          <el-option label="全部作者" value="all" />
          <el-option
            v-for="author in availableAuthors"
            :key="author"
            :label="author"
            :value="author"
          />
        </el-select>

        <el-select
          v-model="safeFilter.publisher"
          placeholder="全部出版社"
          @change="handleFilterChange"
          style="width: 120px"
          filterable
        >
          <el-option label="全部出版社" value="all" />
          <el-option
            v-for="publisher in availablePublishers"
            :key="publisher"
            :label="publisher"
            :value="publisher"
          />
        </el-select>

        <el-button @click="handleClearFilter" :icon="Close" size="small">
          清除筛选
        </el-button>
      </div>

      <div class="toolbar-right">
        <span class="result-count">
          显示 {{ paginatedBooks.length }} / {{ filteredBooks.length }} 本图书
        </span>

        <!-- 排序选择 -->
        <el-dropdown @command="handleSortChange" trigger="click">
          <el-button :icon="ArrowDown">
            排序：{{ getSortLabel() }}
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="title">标题</el-dropdown-item>
              <el-dropdown-item command="author">作者</el-dropdown-item>
              <el-dropdown-item command="addedAt">添加时间</el-dropdown-item>
              <el-dropdown-item command="lastReadAt">最后阅读</el-dropdown-item>
              <el-dropdown-item command="readProgress">阅读进度</el-dropdown-item>
              <el-dropdown-item command="fileSize">文件大小</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>

        <!-- 视图模式切换 -->
        <el-button-group>
          <el-button
            :type="viewMode === 'grid' ? 'primary' : 'default'"
            @click="handleViewModeChange('grid')"
            :icon="Grid"
          />
          <el-button
            :type="viewMode === 'list' ? 'primary' : 'default'"
            @click="handleViewModeChange('list')"
            :icon="List"
          />
        </el-button-group>
      </div>
    </div>

    <!-- 初始化状态 -->
    <div v-if="isInitializing && initializationAttempts <= 1" class="initialization-container">
      <el-card class="initialization-card">
        <div class="initialization-content">
          <el-icon class="initialization-icon" :size="40">
            <Loading />
          </el-icon>
          <h3>正在初始化图书列表</h3>
          <p>请稍候，正在加载您的图书数据...</p>
          <div class="initialization-progress">
            <el-progress :percentage="50" :show-text="false" />
          </div>
        </div>
      </el-card>
    </div>

    <!-- 重试状态 -->
    <div v-else-if="isInitializing && initializationAttempts > 1" class="retry-container">
      <el-card class="retry-card">
        <div class="retry-content">
          <el-icon class="retry-icon" :size="40">
            <RefreshRight />
          </el-icon>
          <h3>正在重试加载</h3>
          <p>第 {{ initializationAttempts }} 次尝试，共 {{ maxInitializationAttempts }} 次</p>
          <div class="retry-progress">
            <el-progress
              :percentage="(initializationAttempts / maxInitializationAttempts) * 100"
              :show-text="false"
              status="warning"
            />
          </div>
        </div>
      </el-card>
    </div>

    <!-- 加载状态 -->
    <div v-else-if="loading" class="loading-container">
      <el-skeleton :rows="6" animated />
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error || (appReady.hasError.value && books.length === 0)" class="error-container">
      <!-- 错误调试信息 -->
      <div class="debug-info" style="background: #ffe6e6; padding: 10px; margin: 10px 0; font-size: 12px; border: 1px solid #ff9999;">
        <p><strong>错误状态调试信息：</strong></p>
        <p>error: {{ error }}</p>
        <p>appReady.hasError.value: {{ appReady.hasError.value }}</p>
        <p>appReady.initializationError.value: {{ appReady.initializationError.value }}</p>
        <p>books.length: {{ books.length }}</p>
        <p>paginatedBooks.length: {{ paginatedBooks.length }}</p>
      </div>
      <el-alert
        :title="error || appReady.initializationError.value || '加载失败'"
        type="error"
        show-icon
        :closable="false"
      />
      <div class="error-actions">
        <el-button @click="handleRefresh" type="primary" :loading="isInitializing">
          {{ isInitializing ? '重试中...' : '重试' }}
        </el-button>
        <el-button @click="handleRetryInitialization" v-if="appReady.hasError.value">
          重新初始化应用
        </el-button>
      </div>
      <div v-if="initializationAttempts > 0" class="retry-info">
        已尝试 {{ initializationAttempts }}/{{ maxInitializationAttempts }} 次
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else-if="paginatedBooks.length === 0" class="empty-container">
      <!-- 调试信息
      <div class="debug-info" style="background: #f0f0f0; padding: 10px; margin: 10px 0; font-size: 12px;">
        <p><strong>调试信息：</strong></p>
        <p>books.length: {{ books.length }}</p>
        <p>filteredBooks.length: {{ filteredBooks.length }}</p>
        <p>paginatedBooks.length: {{ paginatedBooks.length }}</p>
        <p>loading: {{ loading }}</p>
        <p>error: {{ error }}</p>
        <p>isInitializing: {{ isInitializing }}</p>
        <p>appReady.hasError.value: {{ appReady.hasError.value }}</p>
        <p>appReady.initializationError.value: {{ appReady.initializationError.value }}</p>
        <p>viewMode: {{ viewMode }}</p>
        <p>currentPage: {{ currentPage }}</p>
        <p>pageSize: {{ pageSize }}</p>
      </div> -->
      <el-empty
        :description="filteredBooks.length === 0 && books.length === 0 ? '暂无图书，点击添加图书开始阅读' : '没有找到符合条件的图书'"
      >
        <el-button v-if="books.length === 0" type="primary" @click="handleAddBooks">
          添加图书
        </el-button>
        <el-button v-else @click="handleClearFilter">
          清除筛选条件
        </el-button>
      </el-empty>
    </div>

    <!-- 图书列表 - 网格视图 -->
    <div v-else-if="viewMode === 'grid' && paginatedBooks.length > 0" class="books-grid">
      <!-- 调试信息 -->
      <!-- <div class="debug-info" style="background: #e6f7ff; padding: 10px; margin: 10px 0; font-size: 12px; border: 1px solid #91d5ff; grid-column: 1 / -1;">
        <p><strong>图书列表调试信息：</strong></p>
        <p>viewMode: {{ viewMode }}</p>
        <p>paginatedBooks.length: {{ paginatedBooks.length }}</p>
        <p>books.length: {{ books.length }}</p>
        <p>filteredBooks.length: {{ filteredBooks.length }}</p>
        <p>loading: {{ loading }}</p>
        <p>error: {{ error }}</p>
        <p>isInitializing: {{ isInitializing }}</p>
        <p>books数据: {{ books.slice(0, 2).map(b => `${b.title} - ${b.author}`).join(', ') }}</p>
      </div> -->
      <div
        v-for="book in paginatedBooks"
        :key="book.id"
        class="book-card"
        @click="handleBookClick(book)"
      >
        <div class="book-cover">
          <img
            v-if="getCoverUrl(book)"
            :src="getCoverUrl(book)"
            :alt="book.title"
            @error="handleImageError"
          />
          <div v-else class="default-cover">
            <el-icon><Reading /></el-icon>
            <span class="format-badge">{{ (book.format || 'unknown').toUpperCase() }}</span>
          </div>
          
          <!-- 阅读进度 -->
          <div v-if="book.readProgress > 0" class="progress-overlay">
            <el-progress 
              :percentage="book.readProgress" 
              :show-text="false" 
              :stroke-width="3"
            />
          </div>
        </div>

        <div class="book-info">
          <h3 class="book-title" :title="book.title">{{ book.title }}</h3>
          <p class="book-author" :title="book.author || '未知作者'">
            {{ book.author || '未知作者' }}
          </p>
          <div class="book-meta">
            <span class="reading-status" :class="book.readingStatus">
              {{ getReadingStatusText(book.readingStatus) }}
            </span>
            <span class="file-size">{{ formatFileSize(book.fileSize) }}</span>
          </div>
          
          <!-- 标签 -->
          <div v-if="book.tags.length > 0" class="book-tags">
            <el-tag 
              v-for="tag in book.tags.slice(0, 3)" 
              :key="tag" 
              size="small"
              type="info"
            >
              {{ tag }}
            </el-tag>
            <span v-if="book.tags.length > 3" class="more-tags">
              +{{ book.tags.length - 3 }}
            </span>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="book-actions">
          <div class="action-buttons">
            <!-- 编辑图书信息按钮 -->
            <el-button
              link
              type="info"
              :icon="InfoFilled"
              @click.stop="handleShowInfo(book)"
              title="编辑图书信息"
              class="action-btn"
            />

            <!-- 收藏按钮 -->
            <el-button
              link
              :type="book.isFavorite ? 'warning' : 'default'"
              :icon="book.isFavorite ? StarFilled : Star"
              @click.stop="handleToggleFavorite(book)"
              :title="book.isFavorite ? '取消收藏' : '收藏'"
              class="action-btn favorite-btn"
              :class="{ 'is-favorite': book.isFavorite }"
            />

            <!-- 删除按钮 -->
            <el-button
              link
              type="danger"
              :icon="Delete"
              @click.stop="handleRemoveBook(book)"
              title="删除图书"
              class="action-btn delete-btn"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 图书列表 - 列表视图 -->
    <div v-else-if="paginatedBooks.length > 0" class="books-list">
      <el-table 
        :data="paginatedBooks" 
        style="width: 100%"
        @row-click="handleBookClick"
        row-class-name="book-row"
      >
        <el-table-column label="封面" width="80">
          <template #default="{ row }">
            <div class="list-cover">
              <img
                v-if="getCoverUrl(row)"
                :src="getCoverUrl(row)"
                :alt="row.title"
                @error="handleImageError"
              />
              <div v-else class="default-list-cover">
                <el-icon><Reading /></el-icon>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="标题" prop="title" min-width="200">
          <template #default="{ row }">
            <div class="book-title-cell">
              <h4>{{ row.title }}</h4>
              <p class="book-description" v-if="row.description">
                {{ row.description.substring(0, 100) }}{{ row.description.length > 100 ? '...' : '' }}
              </p>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="作者" prop="author" width="150" />
        
        <el-table-column label="格式" width="80">
          <template #default="{ row }">
            <el-tag size="small">{{ row.format.toUpperCase() }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <span class="reading-status" :class="row.readingStatus">
              {{ getReadingStatusText(row.readingStatus) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="进度" width="120">
          <template #default="{ row }">
            <el-progress 
              :percentage="row.readProgress" 
              :stroke-width="6"
              :show-text="true"
            />
          </template>
        </el-table-column>

        <el-table-column label="大小" width="100">
          <template #default="{ row }">
            {{ formatFileSize(row.fileSize) }}
          </template>
        </el-table-column>

        <el-table-column label="添加时间" width="120">
          <template #default="{ row }">
            {{ formatDate(row.addedAt) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <!-- 阅读按钮 -->
              <el-button
                link
                type="primary"
                :icon="Reading"
                @click.stop="handleReadBook(row)"
                title="开始阅读"
                class="action-btn"
              />

              <!-- 编辑图书信息按钮 -->
              <el-button
                link
                type="info"
                :icon="InfoFilled"
                @click.stop="handleShowInfo(row)"
                title="编辑图书信息"
                class="action-btn"
              />

              <!-- 收藏按钮 -->
              <el-button
                link
                :type="row.isFavorite ? 'warning' : 'default'"
                :icon="row.isFavorite ? StarFilled : Star"
                @click.stop="handleToggleFavorite(row)"
                :title="row.isFavorite ? '取消收藏' : '收藏'"
                class="action-btn favorite-btn"
                :class="{ 'is-favorite': row.isFavorite }"
              />

              <!-- 删除按钮 -->
              <el-button
                link
                type="danger"
                :icon="Delete"
                @click.stop="handleRemoveBook(row)"
                title="删除图书"
                class="action-btn delete-btn"
              />
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div v-if="pagination.totalPages > 1" class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @current-change="handlePageChange"
        @size-change="handlePageSizeChange"
      />
    </div>

    <!-- 图书信息编辑对话框 -->
    <BookEditDialog
      v-model="editDialogVisible"
      :book-info="currentEditBook"
      @save="handleSaveBookInfo"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'
import {
  Plus,
  Refresh,
  Search,
  ArrowDown,
  Reading,
  Grid,
  List,
  Close,
  MoreFilled,
  Loading,
  RefreshRight,
  Star,
  StarFilled,
  InfoFilled,
  Delete
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUnifiedLibraryStore } from '../../store/unifiedLibrary'
import { appReady } from '../../composables/useAppReady'
import BookEditDialog from '../../components/BookEditDialog.vue'
import type { BookInfo } from '@shared/types'

// 路由和状态管理
const router = useRouter()
const libraryStore = useUnifiedLibraryStore()

// 从store中解构响应式状态
const {
  books,
  loading,
  error,
  searchQuery,
  filter,
  sortField,
  sortOrder,
  viewMode,
  pageSize,
  currentPage,
  paginatedBooks,
  filteredBooks,
  stats,
  availableFormats,
  availableAuthors,
  availablePublishers,
  availableLanguages,
  availableTags,
  pagination
} = storeToRefs(libraryStore)

// 从store中解构方法（方法不需要storeToRefs）
const {
  loadBooks,
  searchBooks,
  setFilter,
  clearFilter,
  setSort,
  setViewMode,
  goToPage,
  setPageSize,
  addBook,
  removeBook,
  updateProgress,
  getBook,
  setFavorite
} = libraryStore

// 本地响应式状态
const localSearchQuery = ref('')
const coverCache = ref<Map<string, string>>(new Map())
const isInitializing = ref(false)
const initializationAttempts = ref(0)
const maxInitializationAttempts = 3

// 编辑对话框相关状态
const editDialogVisible = ref(false)
const currentEditBook = ref<BookInfo | null>(null)

// 安全的filter访问，确保在组件初始化时不会出错
const safeFilter = computed(() => {
  return filter.value || {
    format: 'all',
    readingStatus: 'all',
    author: 'all',
    publisher: 'all',
    language: 'all',
    tags: []
  }
})

// 监听搜索输入
watch(localSearchQuery, (newValue) => {
  searchBooks(newValue)
}, { debounce: 300 })

// 监听filter变化，同步到safeFilter
watch(filter, (newFilter) => {
  if (newFilter) {
    // 当实际filter更新时，确保safeFilter也更新
    // 这里不需要做任何事，因为safeFilter是计算属性会自动更新
  }
}, { deep: true, immediate: true })

// 计算属性
const getSortLabel = () => {
  const labels: Record<string, string> = {
    title: '标题',
    author: '作者',
    addedAt: '添加时间',
    lastReadAt: '最后阅读',
    readProgress: '阅读进度',
    fileSize: '文件大小'
  }
  return labels[sortField.value] || '默认'
}

// 方法
const handleSearch = (query: string) => {
  localSearchQuery.value = query
}

const handleFilterChange = () => {
  // 将safeFilter的值同步到实际的filter
  if (filter.value) {
    setFilter(safeFilter.value)
  }
}

const handleClearFilter = () => {
  localSearchQuery.value = ''
  clearFilter()
}

const handleViewModeChange = (mode: 'grid' | 'list') => {
  setViewMode(mode)
}

const handleSortChange = (field: string) => {
  setSort(field as any)
}

const handlePageChange = (page: number) => {
  goToPage(page)
}

const handlePageSizeChange = (size: number) => {
  setPageSize(size)
}

const handleRefresh = async () => {
  try {
    await initializeAndLoadBooks()
    ElMessage.success('图书列表刷新成功')
  } catch (error) {
    ElMessage.error('刷新失败')
  }
}

const handleRetryInitialization = async () => {
  try {
    initializationAttempts.value = 0
    const success = await appReady.retryInitialization()
    if (success) {
      ElMessage.success('应用重新初始化成功')
      await initializeAndLoadBooks()
    } else {
      ElMessage.error('应用重新初始化失败')
    }
  } catch (error) {
    ElMessage.error('重新初始化失败')
  }
}

const handleAddBooks = async () => {
  try {
    // 跳转到图书导入页面
    router.push('/import')
  } catch (error) {
    ElMessage.error('跳转到导入页面失败')
  }
}

const handleBookClick = (book: BookInfo) => {
  // 在控制台显示点击图书的详细路径信息
  console.log('=== 图书点击事件 ===')
  console.log('图书ID:', book.id)
  console.log('图书标题:', book.title)
  console.log('文件路径:', book.filePath)
  console.log('封面路径:', book.coverPath)
  console.log('文件格式:', book.format)
  console.log('文件大小:', book.fileSize)
  console.log('路由跳转:', `/reader/${book.id}`)
  console.log('===================')

  // 跳转到统一阅读页面，支持所有格式包括PDF
  router.push(`/reader/${book.id}`)
}

// 处理阅读按钮点击
const handleReadBook = (book: BookInfo) => {
  console.log('=== 开始阅读图书 ===')
  console.log('图书ID:', book.id)
  console.log('图书标题:', book.title)
  console.log('路由跳转:', `/reader/${book.id}`)
  console.log('===================')

  router.push(`/reader/${book.id}`)
}

// 处理编辑图书信息按钮点击
const handleShowInfo = (book: BookInfo) => {
  console.log('=== 打开图书信息编辑对话框 ===')
  console.log('图书ID:', book.id)
  console.log('图书标题:', book.title)
  console.log('===================')

  // 设置当前编辑的图书并打开对话框
  currentEditBook.value = book
  editDialogVisible.value = true
}

// 处理保存图书信息
const handleSaveBookInfo = async (bookData: Partial<BookInfo>) => {
  try {
    if (!currentEditBook.value) {
      ElMessage.error('没有选择要编辑的图书')
      return
    }

    console.log('=== 保存图书信息 ===')
    console.log('图书ID:', currentEditBook.value.id)
    console.log('更新数据:', bookData)
    console.log('===================')

    // 调用API更新图书信息
    const success = await window.electronAPI.book.updateInfo(currentEditBook.value.id, bookData)

    if (success) {
      ElMessage.success('图书信息更新成功')

      // 关闭对话框
      editDialogVisible.value = false
      currentEditBook.value = null

      // 刷新图书列表
      await libraryStore.loadBooks()

      console.log('图书信息更新成功，列表已刷新')
    } else {
      ElMessage.error('图书信息更新失败')
    }
  } catch (error) {
    console.error('保存图书信息失败:', error)
    ElMessage.error(error instanceof Error ? error.message : '保存图书信息失败')
  }
}

// 处理收藏按钮点击
const handleToggleFavorite = async (book: BookInfo) => {
  try {
    const newFavoriteState = !book.isFavorite

    console.log('=== 切换收藏状态 ===')
    console.log('图书ID:', book.id)
    console.log('图书标题:', book.title)
    console.log('当前收藏状态:', book.isFavorite)
    console.log('新收藏状态:', newFavoriteState)
    console.log('===================')

    const success = await libraryStore.setFavorite(book.id, newFavoriteState)

    if (success) {
      ElMessage.success(newFavoriteState ? '已收藏' : '已取消收藏')
    } else {
      ElMessage.error('操作失败')
    }
  } catch (error) {
    console.error('切换收藏状态失败:', error)
    ElMessage.error('操作失败')
  }
}

const handleRemoveBook = async (book: BookInfo) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除《${book.title}》吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await removeBook(book.id)
    ElMessage.success('图书删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.style.display = 'none'
}

// 获取封面URL
const getCoverUrl = (book: BookInfo): string | null => {
  if (!book.coverPath) {
    return null
  }

  // 检查缓存
  if (coverCache.value.has(book.coverPath)) {
    return coverCache.value.get(book.coverPath) || null
  }

  // 异步加载封面
  loadCover(book.coverPath)

  return null
}

// 异步加载封面
const loadCover = async (coverPath: string) => {
  try {
    const dataUrl = await appReady.safeExecute(async () => {
      return await window.electronAPI.book.getCover(coverPath)
    })

    if (dataUrl) {
      coverCache.value.set(coverPath, dataUrl)
    }
  } catch (error) {
    console.error('加载封面失败:', error)
  }
}

// 初始化和加载图书数据
const initializeAndLoadBooks = async (): Promise<void> => {
  if (isInitializing.value) {
    console.log('正在初始化中，跳过重复请求')
    return
  }

  try {
    isInitializing.value = true
    initializationAttempts.value++

    console.log(`开始初始化图书列表 (尝试 ${initializationAttempts.value}/${maxInitializationAttempts})`)

    // 首先确保应用就绪，增加重试次数
    const ready = await appReady.ensureReady(5)
    if (!ready) {
      throw new Error(`应用初始化失败: ${appReady.initializationError.value || '未知错误'}`)
    }

    console.log('应用已就绪，开始加载图书数据...')

    // 加载图书数据
    await loadBooks()

    console.log('图书列表初始化完成')

    // 图书数据加载成功，清除应用错误状态
    if (books.value.length > 0) {
      console.log('图书数据加载成功，清除应用错误状态')
      // 这里可以清除appReady的错误状态，但由于它是只读的，我们通过条件判断来处理
    }

    // 重置重试计数器
    initializationAttempts.value = 0

  } catch (error) {
    console.error('初始化图书列表失败:', error)

    // 如果还有重试机会，延迟后重试
    if (initializationAttempts.value < maxInitializationAttempts) {
      const retryDelay = Math.min(2000 * initializationAttempts.value, 10000) // 递增延迟，最大10秒
      console.log(`将在 ${retryDelay/1000} 秒后重试... (${initializationAttempts.value}/${maxInitializationAttempts})`)

      setTimeout(() => {
        initializeAndLoadBooks()
      }, retryDelay)
    } else {
      ElMessage.error({
        message: '图书列表加载失败，请检查应用状态或尝试刷新页面',
        duration: 5000,
        showClose: true
      })
    }
  } finally {
    isInitializing.value = false
  }
}

// 工具函数
const getReadingStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    unread: '未读',
    reading: '阅读中',
    finished: '已完成'
  }
  return statusMap[status] || '未知'
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (date: Date) => {
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

// 生命周期
onMounted(async () => {
  try {
    console.log('UnifiedLibraryView: 组件已挂载，开始初始化...')

    // 确保响应式变量已初始化
    if (initializationAttempts.value === undefined) {
      initializationAttempts.value = 0
    }

    // 重置初始化状态
    initializationAttempts.value = 0

    // 立即开始初始化，不需要额外延迟
    // 因为main.ts已经确保应用完全就绪后才挂载Vue应用
    await initializeAndLoadBooks()

    // 调试信息
    console.log('UnifiedLibraryView: 初始化完成后的状态检查')
    console.log('books.value:', books.value)
    console.log('filteredBooks.value:', filteredBooks.value)
    console.log('paginatedBooks.value:', paginatedBooks.value)
    console.log('loading.value:', loading.value)
    console.log('error.value:', error.value)
    console.log('isInitializing.value:', isInitializing.value)
  } catch (error) {
    console.error('UnifiedLibraryView: 组件挂载失败:', error)
  }
})
</script>

<style scoped>
.unified-library-view {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 头部区域 */
.library-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
  padding: 6px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 深色模式头部样式 */
html.dark .library-header {
  background: #1e293b;
  border: 1px solid #334155;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.header-left {
  flex: 1;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  color: #303133;
}

.stats-info {
  color: #909399;
  font-size: 14px;
}

/* 深色模式页面标题和统计信息样式 */
html.dark .page-title {
  color: #ffffff;
}

html.dark .stats-info {
  color: #cbd5e1;
}

.header-actions {
  display: flex;
  gap: 12px;
}



/* 工具栏 */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
  padding: 12px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  gap: 16px;
}

/* 深色模式工具栏样式 */
html.dark .toolbar {
  background: #1e293b;
  border: 1px solid #334155;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.toolbar-right {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-shrink: 0;
}

/* 搜索框样式 */
.search-input {
  flex: 1;
  min-width: 200px;
  max-width: 300px;
}

.result-count {
  color: #909399;
  font-size: 14px;
  white-space: nowrap;
}

/* 加载、错误、空状态 */
.loading-container,
.error-container,
.empty-container {
  padding: 40px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

/* 深色模式加载、错误、空状态样式 */
html.dark .loading-container,
html.dark .error-container,
html.dark .empty-container {
  background: #1e293b;
  border: 1px solid #334155;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  color: #ffffff;
}

.error-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 16px;
}

.retry-info {
  margin-top: 12px;
  color: #909399;
  font-size: 12px;
}

/* 网格视图 */
.books-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.book-card {
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
}

.book-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

/* 深色模式图书卡片样式 */
html.dark .book-card {
  background: #1e293b;
  border: 1px solid #334155;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

html.dark .book-card:hover {
  background: #334155;
  border-color: #475569;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
}



/* 深色模式封面容器样式 */
html.dark .book-cover {
  background: #334155;
}

html.dark .book-cover img {
  background: #334155;
}

.book-cover {
  position: relative;
  width: 100%;
  height: 240px; /* 调整高度以适应 3:4 比例 */
  margin-bottom: 12px;
  border-radius: 8px;
  overflow: hidden;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.book-cover img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  background: #f8f9fa;
  border-radius: 6px;
  transition: transform 0.3s ease;
}

.book-card:hover .book-cover img {
  transform: scale(1.02);
}

.default-cover {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.default-cover .el-icon {
  font-size: 48px;
  margin-bottom: 8px;
}

.format-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.progress-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 8px;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
}

.book-info {
  flex: 1;
}

.book-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.book-author {
  margin: 0 0 8px 0;
  color: #909399;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 深色模式图书卡片文本样式 */
html.dark .book-title {
  color: #ffffff;
}

html.dark .book-author {
  color: #cbd5e1;
}

.book-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.reading-status {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.reading-status.unread {
  background: #f0f9ff;
  color: #0369a1;
}

.reading-status.reading {
  background: #fef3c7;
  color: #d97706;
}

.reading-status.finished {
  background: #dcfce7;
  color: #16a34a;
}

/* 深色模式阅读状态样式 */
html.dark .reading-status.unread {
  background: #1e3a8a;
  color: #93c5fd;
}

html.dark .reading-status.reading {
  background: #92400e;
  color: #fbbf24;
}

html.dark .reading-status.finished {
  background: #166534;
  color: #86efac;
}

.file-size {
  color: #909399;
  font-size: 12px;
}

.book-tags {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
  align-items: center;
}

.more-tags {
  color: #909399;
  font-size: 12px;
}

/* 深色模式元数据样式 */
html.dark .file-size {
  color: #cbd5e1;
}

html.dark .more-tags {
  color: #cbd5e1;
}

.book-actions {
  position: absolute;
  top: 12px;
  right: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  padding: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(4px);
}

.book-card:hover .book-actions {
  opacity: 1;
}

/* 卡片视图中的操作按钮样式 */
.book-actions .action-buttons {
  display: flex;
  gap: 4px;
  align-items: center;
}

.book-actions .action-btn {
  min-width: 28px;
  height: 28px;
  padding: 0;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.book-actions .action-btn:hover {
  transform: scale(1.1);
}

/* 深色模式卡片操作按钮 */
html.dark .book-actions {
  background: rgba(30, 41, 59, 0.95);
  border: 1px solid rgba(51, 65, 85, 0.5);
}

/* 护眼模式卡片操作按钮 */
html.theme-eye-care .book-actions {
  background: rgba(251, 248, 240, 0.95);
  border: 1px solid rgba(212, 165, 116, 0.2);
}

/* 列表视图 */
.books-list {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.list-cover {
  width: 48px;
  height: 64px;
  border-radius: 4px;
  overflow: hidden;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.list-cover img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  background: #f8f9fa;
}

.default-list-cover {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.default-list-cover .el-icon {
  font-size: 24px;
}

.book-title-cell h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.book-description {
  margin: 0;
  color: #909399;
  font-size: 12px;
  line-height: 1.4;
}

/* 深色模式样式 */
html.dark .books-list {
  background: #1e293b;
  border: 1px solid #334155;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

html.dark .list-cover {
  background: #334155;
}

html.dark .book-title-cell h4 {
  color: #ffffff;
}

html.dark .book-description {
  color: #cbd5e1;
}

/* Element Plus 表格深色模式样式 - 使用更高优先级的选择器 */
html.dark .books-list .el-table {
  background-color: #1e293b !important;
  color: #ffffff !important;
}

html.dark .books-list .el-table__inner-wrapper {
  background-color: #1e293b !important;
}

html.dark .books-list .el-table__header-wrapper {
  background-color: #1e293b !important;
}

html.dark .books-list .el-table__body-wrapper {
  background-color: #1e293b !important;
}

html.dark .books-list .el-table th.el-table__cell {
  background-color: #334155 !important;
  color: #ffffff !important;
  border-bottom: 1px solid #475569 !important;
}

html.dark .books-list .el-table td.el-table__cell {
  background-color: #1e293b !important;
  color: #ffffff !important;
  border-bottom: 1px solid #334155 !important;
}

html.dark .books-list .el-table tbody tr {
  background-color: #1e293b !important;
}

html.dark .books-list .el-table tbody tr:hover {
  background-color: #334155 !important;
}

html.dark .books-list .el-table tbody tr:hover > td.el-table__cell {
  background-color: #334155 !important;
}

html.dark .books-list .el-table--striped .el-table__body tr.el-table__row--striped td {
  background-color: #2d3748 !important;
}

html.dark .books-list .el-table--striped .el-table__body tr.el-table__row--striped:hover td {
  background-color: #334155 !important;
}

html.dark .books-list .el-table .el-table__empty-block {
  background-color: #1e293b !important;
  color: #cbd5e1 !important;
}

html.dark .books-list .el-table .el-table__empty-text {
  color: #cbd5e1 !important;
}

/* 额外的表格样式覆盖 */
html.dark .books-list .el-table__header {
  background-color: #334155 !important;
}

html.dark .books-list .el-table__body {
  background-color: #1e293b !important;
}

html.dark .books-list .el-table__row {
  background-color: #1e293b !important;
}

html.dark .books-list .el-table__row:hover {
  background-color: #334155 !important;
}

/* 全局Element Plus表格深色模式覆盖 */
html.dark .el-table,
html.dark .el-table__expanded-cell {
  background-color: #1e293b !important;
  color: #ffffff !important;
}

html.dark .el-table th,
html.dark .el-table tr,
html.dark .el-table td {
  background-color: inherit !important;
  color: inherit !important;
  border-color: #334155 !important;
}

html.dark .el-table th {
  background-color: #334155 !important;
  color: #ffffff !important;
}

html.dark .el-table tr:hover {
  background-color: #334155 !important;
}

html.dark .el-table tr:hover td {
  background-color: #334155 !important;
}

/* 强制覆盖Element Plus表格的所有可能的背景色 */
html.dark .el-table .el-table__cell,
html.dark .el-table .el-table__header-wrapper .el-table__cell,
html.dark .el-table .el-table__body-wrapper .el-table__cell,
html.dark .el-table .el-table__footer-wrapper .el-table__cell {
  background-color: inherit !important;
  color: inherit !important;
}

/* 确保表格容器的背景色 */
html.dark .el-table .el-table__inner-wrapper,
html.dark .el-table .el-table__header-wrapper,
html.dark .el-table .el-table__body-wrapper,
html.dark .el-table .el-table__footer-wrapper {
  background-color: #1e293b !important;
}

/* 初始化状态 */
.initialization-container,
.retry-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  padding: 40px;
}

.initialization-card,
.retry-card {
  max-width: 400px;
  width: 100%;
}

.initialization-content,
.retry-content {
  text-align: center;
  padding: 20px;
}

.initialization-icon,
.retry-icon {
  color: #409eff;
  margin-bottom: 16px;
  animation: pulse 2s infinite;
}

.initialization-content h3,
.retry-content h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.initialization-content p,
.retry-content p {
  margin: 0 0 20px 0;
  color: #606266;
  font-size: 14px;
}

/* 深色模式初始化状态文本样式 */
html.dark .initialization-content h3,
html.dark .retry-content h3 {
  color: #ffffff;
}

html.dark .initialization-content p,
html.dark .retry-content p {
  color: #cbd5e1;
}

.initialization-progress,
.retry-progress {
  margin-top: 16px;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

/* 分页 */
.pagination-container {
  display: flex;
  justify-content: center;
  padding: 6px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 深色模式分页样式 */
html.dark .pagination-container {
  background: #1e293b;
  border: 1px solid #334155;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 6px;
  align-items: center;
  justify-content: center;
  padding: 2px;
}

.action-btn {
  padding: 6px 8px !important;
  min-width: 32px;
  height: 32px;
  border-radius: 6px;
  transition: all var(--transition-fast);
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.action-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

/* 阅读按钮样式 */
.action-btn[type="primary"] {
  background-color: var(--el-color-primary);
  border-color: var(--el-color-primary);
  color: white;
}

.action-btn[type="primary"]:hover {
  background-color: var(--el-color-primary-light-3);
  border-color: var(--el-color-primary-light-3);
}

/* 详情按钮样式 */
.action-btn[type="info"] {
  color: var(--el-color-info);
}

.action-btn[type="info"]:hover {
  color: var(--el-color-info-light-3);
  background-color: var(--el-color-info-light-9);
}

/* 收藏按钮样式 */
.favorite-btn {
  color: var(--el-text-color-placeholder);
  transition: all var(--transition-fast);
}

.favorite-btn.is-favorite {
  color: var(--el-color-warning);
}

.favorite-btn:hover {
  color: var(--el-color-warning);
  background-color: var(--el-color-warning-light-9);
}

.favorite-btn.is-favorite:hover {
  color: var(--el-color-warning-light-3);
  background-color: var(--el-color-warning-light-8);
}

/* 删除按钮样式 */
.delete-btn {
  color: var(--el-color-danger);
  transition: all var(--transition-fast);
}

.delete-btn:hover {
  color: var(--el-color-danger-light-3);
  background-color: var(--el-color-danger-light-9);
}

.delete-btn:active {
  color: var(--el-color-danger-dark-2);
  background-color: var(--el-color-danger-light-8);
}

/* 深色模式操作按钮样式 */
html.dark .action-btn:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* 护眼模式操作按钮样式 */
html.theme-eye-care .action-btn:hover {
  box-shadow: 0 2px 8px rgba(93, 78, 55, 0.15);
}

html.theme-eye-care .favorite-btn.is-favorite {
  color: #d4a574;
}

html.theme-eye-care .favorite-btn:hover {
  color: #d4a574;
  background-color: rgba(212, 165, 116, 0.1);
}

/* 深色模式删除按钮样式 */
html.dark .delete-btn {
  color: var(--el-color-danger-light-3);
}

html.dark .delete-btn:hover {
  color: var(--el-color-danger-light-5);
  background-color: rgba(245, 108, 108, 0.15);
}

/* 护眼模式删除按钮样式 */
html.theme-eye-care .delete-btn {
  color: #c4704f;
}

html.theme-eye-care .delete-btn:hover {
  color: #d4805f;
  background-color: rgba(196, 112, 79, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .unified-library-view {
    padding: 6px;
  }

  .library-header {
    flex-direction: column;
    gap: 6px;
    align-items: stretch;
  }

  .toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .toolbar-left {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .search-input {
    min-width: auto;
    max-width: 100%;
  }

  .toolbar-left .el-select {
    width: 100% !important;
  }

  .toolbar-right {
    flex-direction: row;
    justify-content: space-between;
    flex-wrap: wrap;
  }

  .books-grid {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 12px;
  }

  .book-cover {
    height: 200px; /* 在小屏幕上稍微减小高度 */
  }

  /* 移动端操作按钮调整 */
  .action-buttons {
    gap: 4px;
    padding: 1px;
  }

  .action-btn {
    min-width: 28px;
    height: 28px;
    padding: 4px 6px !important;
    font-size: 12px;
    border-radius: 4px;
  }

  .action-btn:hover {
    transform: none; /* 移动端禁用hover动画 */
    box-shadow: none;
  }

  /* 卡片视图操作按钮移动端调整 */
  .book-actions {
    top: 8px;
    right: 8px;
    padding: 2px;
  }

  .book-actions .action-buttons {
    gap: 2px;
  }

  .book-actions .action-btn {
    min-width: 24px;
    height: 24px;
  }
}

@media (max-width: 480px) {
  .books-grid {
    grid-template-columns: 1fr;
  }

  /* 超小屏幕操作按钮进一步优化 */
  .action-buttons {
    gap: 2px;
  }

  .action-btn {
    min-width: 24px;
    height: 24px;
    padding: 2px 4px !important;
    font-size: 11px;
  }

  /* 超小屏幕卡片视图操作按钮 */
  .book-actions {
    top: 6px;
    right: 6px;
    padding: 1px;
  }

  .book-actions .action-btn {
    min-width: 20px;
    height: 20px;
  }
}
</style>
