/**
 * 现代化EPUB阅读器IPC处理器
 * 处理渲染进程与主进程之间的EPUB相关通信
 * 基于重新实现的EpubParser、EpubReader等服务，提供真实的EPUB解析功能
 */

import { ipcMain } from 'electron'
import { existsSync, statSync } from 'fs'
import { basename, extname, normalize } from 'path'
import { EpubParser } from '@shared/services/epub/EpubParser'
import { EpubReader } from '@shared/services/epub/EpubReader'
import type { 
  EpubReaderConfig,
  EpubRenderConfig,
  EpubParseResult,
  EpubBookInfo,
  EpubChapter,
  EpubSearchResult,
  ReadingPosition
} from '@shared/services/epub/types'

/**
 * 阅读器实例接口
 */
interface EpubReaderInstance {
  id: string
  filePath: string
  parser: EpubParser
  reader: EpubReader
  parseResult: EpubParseResult
  createdAt: number
  lastAccessedAt: number
}

/**
 * 创建进度接口
 */
interface EpubInstanceCreationProgress {
  readerId: string
  stage: string
  progress: number
  message: string
  timeElapsed: number
  error?: string
}

/**
 * 现代化EPUB阅读器IPC处理器类
 */
export class EpubReaderIPCHandler {
  private readers = new Map<string, EpubReaderInstance>()
  private creationProgress = new Map<string, EpubInstanceCreationProgress>()

  constructor() {
    console.log('EpubReaderIPCHandler: 初始化现代化EPUB阅读器IPC处理器')
  }

  /**
   * 注册IPC处理器
   */
  registerHandlers(): void {
    // 解析EPUB文件
    ipcMain.handle('epub-reader:parse-epub', async (_, filePath: string) => {
      return this.handleParseEpub(filePath)
    })

    // 获取EPUB文件信息
    ipcMain.handle('epub-reader:get-file-info', async (_, filePath: string) => {
      return this.handleGetFileInfo(filePath)
    })

    // 创建EPUB阅读器实例
    ipcMain.handle('epub-reader:create', async (_, readerId: string, filePath: string, config?: EpubReaderConfig) => {
      return this.handleCreateReader(readerId, filePath, config)
    })

    // 获取创建进度
    ipcMain.handle('epub-reader:get-creation-progress', async (_, readerId: string) => {
      return this.getCreationProgress(readerId)
    })

    // 销毁EPUB阅读器实例
    ipcMain.handle('epub-reader:destroy', async (_, readerId: string) => {
      return this.handleDestroyReader(readerId)
    })

    // 获取章节内容
    ipcMain.handle('epub-reader:get-chapter', async (_, readerId: string, chapterIndex: number) => {
      return this.handleGetChapter(readerId, chapterIndex)
    })

    // 获取目录结构
    ipcMain.handle('epub-reader:get-toc', async (_, readerId: string) => {
      return this.handleGetToc(readerId)
    })

    // 搜索内容
    ipcMain.handle('epub-reader:search', async (_, readerId: string, query: string) => {
      return this.handleSearch(readerId, query)
    })

    // 导航操作
    ipcMain.handle('epub-reader:navigate', async (_, readerId: string, action: string, params?: any) => {
      return this.handleNavigate(readerId, action, params)
    })

    // 获取阅读器状态
    ipcMain.handle('epub-reader:get-status', async (_, readerId: string) => {
      return this.handleGetStatus(readerId)
    })

    // 更新位置
    ipcMain.handle('epub-reader:update-position', async (_, readerId: string, position: Partial<ReadingPosition>) => {
      return this.handleUpdatePosition(readerId, position)
    })

    // 获取调试信息
    ipcMain.handle('epub-reader:get-debug-info', async (_, readerId: string) => {
      return this.handleGetDebugInfo(readerId)
    })

    console.log('EpubReaderIPCHandler: 所有IPC处理器已注册')
  }

  /**
   * 处理解析EPUB文件
   */
  private async handleParseEpub(filePath: string): Promise<{
    success: boolean
    result?: EpubParseResult
    error?: string
  }> {
    try {
      console.log(`EpubReaderIPC: 开始解析EPUB文件 ${filePath}`)

      // 验证文件
      const fileValidation = this.validateFile(filePath)
      if (!fileValidation.isValid) {
        throw new Error(fileValidation.error)
      }

      // 创建解析器并快速解析（只解析基本信息）
      const parser = new EpubParser()
      const result = await parser.parseEpubBasic(filePath)

      console.log(`EpubReaderIPC: 解析完成，书名: ${result.bookInfo.title}`)
      return {
        success: true,
        result
      }

    } catch (error) {
      console.error('EpubReaderIPC: 解析EPUB失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '解析失败'
      }
    }
  }

  /**
   * 处理获取文件信息
   */
  private async handleGetFileInfo(filePath: string): Promise<{
    success: boolean
    fileInfo?: any
    error?: string
  }> {
    try {
      const validation = this.validateFile(filePath)
      if (!validation.isValid) {
        throw new Error(validation.error)
      }

      const stats = statSync(filePath)
      const fileInfo = {
        path: filePath,
        name: basename(filePath),
        size: stats.size,
        lastModified: stats.mtime,
        isValid: true
      }

      return {
        success: true,
        fileInfo
      }

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取文件信息失败'
      }
    }
  }

  /**
   * 处理创建阅读器实例
   */
  private async handleCreateReader(
    readerId: string, 
    filePath: string, 
    config?: EpubReaderConfig
  ): Promise<{
    success: boolean
    error?: string
  }> {
    try {
      console.log(`EpubReaderIPC: 开始创建阅读器实例 ${readerId}`)

      // 初始化进度跟踪
      this.initCreationProgress(readerId)

      // 验证文件
      this.updateCreationProgress(readerId, 'validation', 10, '验证文件...')
      const fileValidation = this.validateFile(filePath)
      if (!fileValidation.isValid) {
        throw new Error(fileValidation.error)
      }

      // 创建解析器和阅读器
      this.updateCreationProgress(readerId, 'creation', 30, '创建组件...')
      const parser = new EpubParser()
      const reader = new EpubReader(config)

      // 快速解析EPUB文件（只解析基本信息）
      this.updateCreationProgress(readerId, 'parsing', 50, '快速解析EPUB文件...')
      const parseResult = await parser.parseEpubBasic(filePath)

      // 设置阅读器的解析结果和解析器实例
      this.updateCreationProgress(readerId, 'setup', 80, '设置阅读器...')
      reader.setParseResult(parseResult)
      reader.setParser(parser)  // 设置已解析的parser实例

      // 创建阅读器实例
      const readerInstance: EpubReaderInstance = {
        id: readerId,
        filePath,
        parser,
        reader,
        parseResult,
        createdAt: Date.now(),
        lastAccessedAt: Date.now()
      }

      // 保存实例
      this.readers.set(readerId, readerInstance)

      this.updateCreationProgress(readerId, 'complete', 100, '创建完成')
      console.log(`EpubReaderIPC: 阅读器实例创建成功 ${readerId}`)

      return { success: true }

    } catch (error) {
      console.error(`EpubReaderIPC: 创建阅读器实例失败 ${readerId}:`, error)
      this.updateCreationProgress(readerId, 'error', 0, '创建失败', error instanceof Error ? error.message : '未知错误')
      
      return {
        success: false,
        error: error instanceof Error ? error.message : '创建阅读器失败'
      }
    }
  }

  /**
   * 处理获取章节内容
   */
  private async handleGetChapter(readerId: string, chapterIndex: number): Promise<{
    success: boolean
    content?: string
    chapter?: EpubChapter
    error?: string
  }> {
    try {
      const instance = this.getReaderInstance(readerId)
      const chapter = await instance.reader.getChapter(chapterIndex)
      
      instance.lastAccessedAt = Date.now()

      return {
        success: true,
        content: chapter.content,
        chapter
      }

    } catch (error) {
      console.error(`EpubReaderIPC: 获取章节失败 ${readerId}:`, error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取章节失败'
      }
    }
  }

  /**
   * 处理获取目录
   */
  private async handleGetToc(readerId: string): Promise<{
    success: boolean
    chapters?: any[]
    bookInfo?: EpubBookInfo
    error?: string
  }> {
    try {
      const instance = this.getReaderInstance(readerId)
      const tocData = instance.reader.getToc()
      
      instance.lastAccessedAt = Date.now()

      return {
        success: true,
        chapters: tocData.toc, // 返回目录结构
        bookInfo: tocData.bookInfo,
        toc: tocData.toc // 同时提供toc字段以保持兼容性
      }

    } catch (error) {
      console.error(`EpubReaderIPC: 获取目录失败 ${readerId}:`, error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取目录失败'
      }
    }
  }

  /**
   * 处理搜索
   */
  private async handleSearch(readerId: string, query: string): Promise<EpubSearchResult[]> {
    try {
      const instance = this.getReaderInstance(readerId)
      const results = await instance.reader.search(query)
      
      instance.lastAccessedAt = Date.now()
      return results

    } catch (error) {
      console.error(`EpubReaderIPC: 搜索失败 ${readerId}:`, error)
      return []
    }
  }

  /**
   * 处理导航
   */
  private async handleNavigate(readerId: string, action: string, params?: any): Promise<{
    success: boolean
    error?: string
  }> {
    try {
      const instance = this.getReaderInstance(readerId)
      
      switch (action) {
        case 'goToChapter':
          await instance.reader.goToChapter(params.chapterIndex)
          break
        default:
          throw new Error(`未知的导航操作: ${action}`)
      }
      
      instance.lastAccessedAt = Date.now()
      return { success: true }

    } catch (error) {
      console.error(`EpubReaderIPC: 导航失败 ${readerId}:`, error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '导航失败'
      }
    }
  }

  /**
   * 处理更新位置
   */
  private async handleUpdatePosition(readerId: string, position: Partial<ReadingPosition>): Promise<{
    success: boolean
    error?: string
  }> {
    try {
      const instance = this.getReaderInstance(readerId)
      instance.reader.updatePosition(position)
      
      instance.lastAccessedAt = Date.now()
      return { success: true }

    } catch (error) {
      console.error(`EpubReaderIPC: 更新位置失败 ${readerId}:`, error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '更新位置失败'
      }
    }
  }

  /**
   * 处理获取状态
   */
  private async handleGetStatus(readerId: string): Promise<any> {
    try {
      const instance = this.getReaderInstance(readerId)
      const status = instance.reader.getStatus()
      
      instance.lastAccessedAt = Date.now()
      return status

    } catch (error) {
      console.error(`EpubReaderIPC: 获取状态失败 ${readerId}:`, error)
      return null
    }
  }

  /**
   * 处理销毁阅读器
   */
  private async handleDestroyReader(readerId: string): Promise<boolean> {
    try {
      const instance = this.readers.get(readerId)
      if (instance) {
        instance.reader.destroy()
        instance.parser.destroy()
        this.readers.delete(readerId)
        this.creationProgress.delete(readerId)
        console.log(`EpubReaderIPC: 阅读器实例已销毁 ${readerId}`)
      }
      return true

    } catch (error) {
      console.error(`EpubReaderIPC: 销毁阅读器失败 ${readerId}:`, error)
      return false
    }
  }

  /**
   * 获取阅读器实例
   */
  private getReaderInstance(readerId: string): EpubReaderInstance {
    const instance = this.readers.get(readerId)
    if (!instance) {
      throw new Error(`阅读器实例不存在: ${readerId}`)
    }
    return instance
  }

  /**
   * 验证文件
   */
  private validateFile(filePath: string): { isValid: boolean; error?: string } {
    try {
      if (!filePath || typeof filePath !== 'string') {
        return { isValid: false, error: '文件路径无效' }
      }

      if (!existsSync(filePath)) {
        return { isValid: false, error: '文件不存在' }
      }

      const ext = extname(filePath).toLowerCase()
      if (ext !== '.epub') {
        return { isValid: false, error: '不是有效的EPUB文件' }
      }

      const stats = statSync(filePath)
      if (stats.size === 0) {
        return { isValid: false, error: '文件为空' }
      }

      return { isValid: true }

    } catch (error) {
      return { 
        isValid: false, 
        error: `文件验证失败: ${error instanceof Error ? error.message : '未知错误'}` 
      }
    }
  }

  /**
   * 初始化创建进度
   */
  private initCreationProgress(readerId: string): void {
    this.creationProgress.set(readerId, {
      readerId,
      stage: 'init',
      progress: 0,
      message: '开始创建...',
      timeElapsed: 0
    })
  }

  /**
   * 更新创建进度
   */
  private updateCreationProgress(
    readerId: string, 
    stage: string, 
    progress: number, 
    message: string, 
    error?: string
  ): void {
    const current = this.creationProgress.get(readerId)
    if (current) {
      this.creationProgress.set(readerId, {
        ...current,
        stage,
        progress,
        message,
        timeElapsed: Date.now() - (current.timeElapsed || Date.now()),
        error
      })
    }
  }

  /**
   * 获取创建进度
   */
  private getCreationProgress(readerId: string): EpubInstanceCreationProgress | null {
    return this.creationProgress.get(readerId) || null
  }

  /**
   * 注销所有处理器
   */
  unregisterHandlers(): void {
    const handlers = [
      'epub-reader:parse-epub',
      'epub-reader:get-file-info',
      'epub-reader:create',
      'epub-reader:get-creation-progress',
      'epub-reader:destroy',
      'epub-reader:get-chapter',
      'epub-reader:get-toc',
      'epub-reader:search',
      'epub-reader:navigate',
      'epub-reader:get-status',
      'epub-reader:update-position'
    ]

    handlers.forEach(handler => {
      ipcMain.removeAllListeners(handler)
    })

    // 清理所有实例
    this.readers.forEach(instance => {
      instance.reader.destroy()
      instance.parser.destroy()
    })
    this.readers.clear()
    this.creationProgress.clear()

    console.log('EpubReaderIPCHandler: 所有处理器已注销')
  }

  /**
   * 处理获取调试信息
   */
  private async handleGetDebugInfo(readerId: string): Promise<{
    success: boolean
    debugInfo?: any
    error?: string
  }> {
    try {
      const instance = this.readers.get(readerId)
      if (!instance) {
        return {
          success: false,
          error: '阅读器实例不存在'
        }
      }

      // 获取解析器的调试信息
      const debugInfo = (instance.parser as any).getDebugInfo?.()

      return {
        success: true,
        debugInfo: {
          readerId,
          filePath: instance.filePath,
          createdAt: new Date(instance.createdAt).toISOString(),
          lastAccessedAt: new Date(instance.lastAccessedAt).toISOString(),
          bookInfo: instance.parseResult.bookInfo,
          parserDebugInfo: debugInfo
        }
      }

    } catch (error) {
      console.error('EpubReaderIPC: 获取调试信息失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取调试信息失败'
      }
    }
  }
}
