/**
 * 图书相关IPC处理器
 * 重新设计的IPC处理器，解决字段不匹配和错误处理问题
 */

import { ipcMain } from 'electron'
import { BookService } from '../services/BookService'
import type { BookInfo } from '@shared/types'

export class BookIPCHandler {
  constructor(private bookService: BookService) {}

  /**
   * 注册所有图书相关的IPC处理器
   */
  registerHandlers(): void {
    // 获取图书列表
    ipcMain.handle('book:list', async (): Promise<BookInfo[]> => {
      try {
        console.log('IPC: 开始获取图书列表')
        const books = await this.bookService.getAllBooks()
        console.log(`IPC: 成功获取 ${books.length} 本图书`)
        return books
      } catch (error) {
        console.error('IPC: 获取图书列表失败:', error)
        throw new Error(error instanceof Error ? error.message : '获取图书列表失败')
      }
    })

    // 获取单本图书
    ipcMain.handle('book:get', async (_, bookId: string): Promise<BookInfo | null> => {
      try {
        console.log(`IPC: 开始获取图书 ${bookId}`)
        
        if (!bookId || typeof bookId !== 'string') {
          throw new Error('图书ID无效')
        }
        
        const book = await this.bookService.getBookById(bookId)
        console.log(`IPC: ${book ? '成功获取' : '未找到'}图书 ${bookId}`)
        return book
      } catch (error) {
        console.error(`IPC: 获取图书失败 ${bookId}:`, error)
        throw new Error(error instanceof Error ? error.message : '获取图书失败')
      }
    })

    // 添加图书
    ipcMain.handle('book:add', async (_, filePath: string): Promise<BookInfo> => {
      try {
        console.log(`IPC: 开始添加图书 ${filePath}`)

        if (!filePath || typeof filePath !== 'string') {
          throw new Error('文件路径无效')
        }

        const book = await this.bookService.addBook(filePath)
        console.log(`IPC: 成功添加图书 ${book.title}`)
        return book
      } catch (error) {
        console.error(`IPC: 添加图书失败 ${filePath}:`, error)
        throw new Error(error instanceof Error ? error.message : '添加图书失败')
      }
    })

    // 删除图书
    ipcMain.handle('book:remove', async (_, bookId: string): Promise<boolean> => {
      try {
        console.log(`IPC: 开始删除图书 ${bookId}`)

        if (!bookId || typeof bookId !== 'string') {
          throw new Error('图书ID无效')
        }

        const success = await this.bookService.removeBook(bookId)
        console.log(`IPC: ${success ? '成功' : '失败'}删除图书 ${bookId}`)
        return success
      } catch (error) {
        console.error(`IPC: 删除图书失败 ${bookId}:`, error)
        throw new Error(error instanceof Error ? error.message : '删除图书失败')
      }
    })

    // 添加图书（带详细信息）
    ipcMain.handle('book:add-with-details', async (_, bookData: any): Promise<BookInfo> => {
      try {
        console.log(`IPC: 开始添加图书（带详细信息）`)

        if (!bookData || typeof bookData !== 'object') {
          throw new Error('图书数据无效')
        }

        if (!bookData.filePath || typeof bookData.filePath !== 'string') {
          throw new Error('文件路径无效')
        }

        const book = await this.bookService.addBookWithDetails(bookData)
        console.log(`IPC: 成功添加图书 ${book.title}`)
        return book
      } catch (error) {
        console.error(`IPC: 添加图书失败:`, error)
        throw new Error(error instanceof Error ? error.message : '添加图书失败')
      }
    })

    // 提取图书元数据
    ipcMain.handle('book:extract-metadata', async (_, filePath: string) => {
      try {
        console.log(`IPC: 提取图书元数据 ${filePath}`)

        if (!filePath || typeof filePath !== 'string') {
          throw new Error('文件路径无效')
        }

        const metadata = await this.bookService.extractBookMetadata(filePath)
        console.log(`IPC: 成功提取元数据`)
        return metadata
      } catch (error) {
        console.error(`IPC: 提取元数据失败:`, error)
        throw new Error(error instanceof Error ? error.message : '提取元数据失败')
      }
    })

    // 更新阅读进度
    ipcMain.handle('book:update-progress', async (
      _, 
      bookId: string, 
      progress: number, 
      currentPage?: number
    ): Promise<boolean> => {
      try {
        console.log(`IPC: 开始更新阅读进度 ${bookId}, 进度: ${progress}%`)
        
        if (!bookId || typeof bookId !== 'string') {
          throw new Error('图书ID无效')
        }
        
        if (typeof progress !== 'number' || progress < 0 || progress > 100) {
          throw new Error('阅读进度必须是0-100之间的数字')
        }
        
        if (currentPage !== undefined && (typeof currentPage !== 'number' || currentPage < 0)) {
          throw new Error('当前页码必须是非负数')
        }
        
        const success = await this.bookService.updateProgress(bookId, progress, currentPage)
        console.log(`IPC: ${success ? '成功' : '失败'}更新阅读进度 ${bookId}`)
        return success
      } catch (error) {
        console.error(`IPC: 更新阅读进度失败 ${bookId}:`, error)
        throw new Error(error instanceof Error ? error.message : '更新阅读进度失败')
      }
    })

    // 更新图书信息
    ipcMain.handle('book:update-info', async (_, bookId: string, bookData: Partial<BookInfo>): Promise<boolean> => {
      try {
        console.log(`IPC: 开始更新图书信息 ${bookId}`, bookData)

        if (!bookId || typeof bookId !== 'string') {
          throw new Error('图书ID无效')
        }

        if (!bookData || typeof bookData !== 'object') {
          throw new Error('图书数据无效')
        }

        // 验证必要字段
        if (bookData.title !== undefined && (!bookData.title || bookData.title.trim().length === 0)) {
          throw new Error('图书标题不能为空')
        }

        // 验证数字字段
        if (bookData.totalPages !== undefined && (typeof bookData.totalPages !== 'number' || bookData.totalPages < 0)) {
          throw new Error('总页数必须是非负数')
        }

        if (bookData.wordCount !== undefined && (typeof bookData.wordCount !== 'number' || bookData.wordCount < 0)) {
          throw new Error('字数必须是非负数')
        }

        // 验证标签数组
        if (bookData.tags !== undefined && !Array.isArray(bookData.tags)) {
          throw new Error('标签必须是数组格式')
        }

        const success = await this.bookService.updateBookInfo(bookId, bookData)
        console.log(`IPC: ${success ? '成功' : '失败'}更新图书信息 ${bookId}`)
        return success
      } catch (error) {
        console.error(`IPC: 更新图书信息失败 ${bookId}:`, error)
        throw new Error(error instanceof Error ? error.message : '更新图书信息失败')
      }
    })

    // 搜索图书
    ipcMain.handle('book:search', async (_, query: string, limit?: number): Promise<BookInfo[]> => {
      try {
        console.log(`IPC: 开始搜索图书 "${query}"`)
        
        if (!query || typeof query !== 'string') {
          throw new Error('搜索关键词无效')
        }
        
        if (query.trim().length === 0) {
          return []
        }
        
        const searchLimit = limit && typeof limit === 'number' && limit > 0 ? limit : 50
        const books = await this.bookService.searchBooks(query.trim(), searchLimit)
        console.log(`IPC: 搜索到 ${books.length} 本图书`)
        return books
      } catch (error) {
        console.error(`IPC: 搜索图书失败 "${query}":`, error)
        throw new Error(error instanceof Error ? error.message : '搜索图书失败')
      }
    })

    // 获取图书统计信息
    ipcMain.handle('book:stats', async (): Promise<{
      total: number
      unread: number
      reading: number
      finished: number
    }> => {
      try {
        console.log('IPC: 开始获取图书统计信息')
        const books = await this.bookService.getAllBooks()
        
        const stats = {
          total: books.length,
          unread: books.filter(book => book.readProgress === 0).length,
          reading: books.filter(book => book.readProgress > 0 && book.readProgress < 100).length,
          finished: books.filter(book => book.readProgress >= 100).length
        }
        
        console.log('IPC: 成功获取图书统计信息', stats)
        return stats
      } catch (error) {
        console.error('IPC: 获取图书统计信息失败:', error)
        throw new Error(error instanceof Error ? error.message : '获取图书统计信息失败')
      }
    })

    // 批量操作：删除多本图书
    ipcMain.handle('book:remove-batch', async (_, bookIds: string[]): Promise<{
      success: number
      failed: number
      errors: string[]
    }> => {
      try {
        console.log(`IPC: 开始批量删除 ${bookIds.length} 本图书`)
        
        if (!Array.isArray(bookIds) || bookIds.length === 0) {
          throw new Error('图书ID列表无效')
        }
        
        let success = 0
        let failed = 0
        const errors: string[] = []
        
        for (const bookId of bookIds) {
          try {
            const result = await this.bookService.removeBook(bookId)
            if (result) {
              success++
            } else {
              failed++
              errors.push(`删除图书失败: ${bookId}`)
            }
          } catch (error) {
            failed++
            errors.push(`删除图书失败 ${bookId}: ${error instanceof Error ? error.message : '未知错误'}`)
          }
        }
        
        const result = { success, failed, errors }
        console.log('IPC: 批量删除完成', result)
        return result
      } catch (error) {
        console.error('IPC: 批量删除图书失败:', error)
        throw new Error(error instanceof Error ? error.message : '批量删除图书失败')
      }
    })

    // 获取封面图片
    ipcMain.handle('book:get-cover', async (_, coverPath: string): Promise<string | null> => {
      try {
        console.log(`IPC: 开始获取封面图片 ${coverPath}`)

        if (!coverPath || typeof coverPath !== 'string') {
          return null
        }

        const fs = require('fs')
        const path = require('path')

        // 检查文件是否存在
        if (!fs.existsSync(coverPath)) {
          console.log(`IPC: 封面文件不存在 ${coverPath}`)
          return null
        }

        // 读取文件并转换为base64
        const fileBuffer = fs.readFileSync(coverPath)
        const ext = path.extname(coverPath).toLowerCase()

        let mimeType = 'image/png'
        if (ext === '.jpg' || ext === '.jpeg') {
          mimeType = 'image/jpeg'
        } else if (ext === '.svg') {
          mimeType = 'image/svg+xml'
        } else if (ext === '.png') {
          mimeType = 'image/png'
        }

        const base64 = fileBuffer.toString('base64')
        const dataUrl = `data:${mimeType};base64,${base64}`

        console.log(`IPC: 成功获取封面图片 ${coverPath}`)
        return dataUrl

      } catch (error) {
        console.error(`IPC: 获取封面图片失败 ${coverPath}:`, error)
        return null
      }
    })

    // 设置收藏状态
    ipcMain.handle('book:set-favorite', async (_, bookId: string, isFavorite: boolean): Promise<boolean> => {
      try {
        console.log(`IPC: 开始设置图书收藏状态 ${bookId} -> ${isFavorite}`)

        if (!bookId || typeof bookId !== 'string') {
          throw new Error('图书ID无效')
        }

        const success = await this.bookService.setFavorite(bookId, isFavorite)
        console.log(`IPC: ${success ? '成功' : '失败'}设置图书收藏状态 ${bookId}`)
        return success
      } catch (error) {
        console.error(`IPC: 设置图书收藏状态失败 ${bookId}:`, error)
        throw new Error(error instanceof Error ? error.message : '设置收藏状态失败')
      }
    })

    console.log('图书IPC处理器注册完成')
  }

  /**
   * 注销所有处理器
   */
  unregisterHandlers(): void {
    const handlers = [
      'book:list',
      'book:get',
      'book:add',
      'book:remove',
      'book:update-progress',
      'book:update-info',
      'book:search',
      'book:stats',
      'book:remove-batch',
      'book:get-cover',
      'book:set-favorite'
    ]
    
    handlers.forEach(handler => {
      ipcMain.removeAllListeners(handler)
    })
    
    console.log('图书IPC处理器注销完成')
  }
}
