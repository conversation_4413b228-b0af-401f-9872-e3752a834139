<!--
  PDF阅读器视图组件
  使用@tato30/vue-pdf进行PDF文档的渲染和交互
-->

<template>
  <div class="pdf-reader-view">
    <!-- 工具栏 -->
    <div class="toolbar">
      <!-- 返回按钮 -->
      <div class="toolbar-left">
        <el-button @click="goBack" type="primary" :icon="ArrowLeft">
          返回图书列表
        </el-button>
      </div>

      <!-- 书籍信息 -->
      <div class="book-info" v-if="currentBook">
        <span class="book-title">{{ currentBook.title }}</span>
        <span class="reading-progress">{{ progressText }}</span>
      </div>

      <!-- 工具按钮区域 -->
      <div class="toolbar-right">
        <!-- 页面导航控制 -->
        <div class="page-controls">
          <el-button
            @click="previousPage"
            :disabled="currentPage <= 1"
            :icon="ArrowLeft"
            size="small"
            title="上一页"
          />

          <div class="page-info">
            <el-input
              v-model="pageInputValue"
              @keyup.enter="goToPage"
              @blur="goToPage"
              size="small"
              class="page-input"
              :placeholder="currentPage.toString()"
            />
            <span class="page-total">/ {{ totalPages }}</span>
          </div>

          <el-button
            @click="nextPage"
            :disabled="currentPage >= totalPages"
            :icon="ArrowRight"
            size="small"
            title="下一页"
          />
        </div>

        <!-- 缩放控制 -->
        <div class="zoom-controls">
          <el-button @click="zoomOut" :icon="ZoomOut" size="small" title="缩小" />
          <span class="zoom-level">{{ Math.round(scale * 100) }}%</span>
          <el-button @click="zoomIn" :icon="ZoomIn" size="small" title="放大" />
          <el-button @click="resetZoom" size="small" title="适应页面">适应</el-button>
        </div>

        <!-- 搜索按钮 -->
        <el-button
          :icon="Search"
          @click="toggleSearch"
          size="small"
          title="搜索文本"
          :type="showSearch ? 'primary' : 'default'"
        />

        <!-- 侧边栏切换 -->
        <el-dropdown @command="handleSidebarCommand" trigger="click">
          <el-button
            :icon="Menu"
            size="small"
            title="侧边栏"
            :type="showSidebar ? 'primary' : 'default'"
          />
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="toggle">
                {{ showSidebar ? '隐藏侧边栏' : '显示侧边栏' }}
              </el-dropdown-item>
              <el-dropdown-item divided command="thumbnails" :disabled="!showSidebar">
                缩略图模式
              </el-dropdown-item>
              <el-dropdown-item command="bookmarks" :disabled="!showSidebar">
                书签模式
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>

        <!-- 设置按钮 -->
        <el-button
          :icon="Setting"
          @click="toggleSettings"
          size="small"
          title="设置"
          :type="showSettings ? 'primary' : 'default'"
        />
      </div>
    </div>



    <!-- 搜索面板 -->
    <div v-if="showSearch" class="search-panel">
      <div class="search-header">
        <h3>搜索文档</h3>
        <el-button link @click="toggleSearch" :icon="Close" size="small" />
      </div>
      <div class="search-content">
        <el-input
          v-model="searchQuery"
          placeholder="输入搜索关键词..."
          @keyup.enter="performSearch"
          clearable
        >
          <template #append>
            <el-button @click="performSearch" :icon="Search" />
          </template>
        </el-input>
        <div class="search-results" v-if="searchResults.length > 0">
          <div class="search-stats">
            找到 {{ searchResults.length }} 个结果
          </div>
          <div class="search-list">
            <div
              v-for="(result, index) in searchResults"
              :key="index"
              class="search-item"
              @click="goToSearchResult(result)"
            >
              <div class="search-text">{{ result.text }}</div>
              <div class="search-page">第 {{ result.page }} 页</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 设置面板 -->
    <el-drawer
      v-model="showSettings"
      title="PDF阅读设置"
      direction="rtl"
      size="400px"
    >
      <div class="settings-container">
        <div class="setting-group">
          <h4>显示设置</h4>
          <div class="setting-item">
            <label>缩放级别</label>
            <el-slider
              v-model="scale"
              :min="0.25"
              :max="5.0"
              :step="0.25"
              :format-tooltip="(val) => `${Math.round(val * 100)}%`"
            />
          </div>
          <div class="setting-item">
            <label>显示模式</label>
            <el-radio-group v-model="displayMode">
              <el-radio value="single">单页显示</el-radio>
              <el-radio value="continuous">连续滚动</el-radio>
            </el-radio-group>
          </div>
        </div>

        <div class="setting-group">
          <h4>侧边栏设置</h4>
          <div class="setting-item">
            <label>默认显示模式</label>
            <el-radio-group v-model="sidebarMode">
              <el-radio value="thumbnails">缩略图</el-radio>
              <el-radio value="bookmarks">书签</el-radio>
            </el-radio-group>
          </div>
          <div class="setting-item">
            <label>启动时显示侧边栏</label>
            <el-switch v-model="autoShowSidebar" />
          </div>
        </div>

        <div class="setting-group">
          <h4>阅读设置</h4>
          <div class="setting-item">
            <label>记住阅读位置</label>
            <el-switch v-model="rememberPosition" />
          </div>
          <div class="setting-item">
            <label>自动保存进度</label>
            <el-switch v-model="autoSaveProgress" />
          </div>
        </div>

        <div class="setting-group">
          <h4>快捷键说明</h4>
          <div class="shortcut-list">
            <div class="shortcut-item">
              <span class="shortcut-key">Ctrl+F</span>
              <span class="shortcut-desc">搜索文本</span>
            </div>
            <div class="shortcut-item">
              <span class="shortcut-key">Ctrl+S</span>
              <span class="shortcut-desc">切换侧边栏</span>
            </div>
            <div class="shortcut-item">
              <span class="shortcut-key">←/→</span>
              <span class="shortcut-desc">翻页</span>
            </div>
            <div class="shortcut-item">
              <span class="shortcut-key">+/-</span>
              <span class="shortcut-desc">缩放</span>
            </div>
            <div class="shortcut-item">
              <span class="shortcut-key">0</span>
              <span class="shortcut-desc">重置缩放</span>
            </div>
          </div>
        </div>
      </div>
    </el-drawer>

    <!-- 主内容区域 -->
    <div class="content-area">
      <!-- 侧边栏 -->
      <div v-if="showSidebar" class="sidebar">
        <!-- 缩略图面板 -->
        <div v-if="sidebarMode === 'thumbnails'" class="thumbnails-panel">
          <h3>缩略图</h3>
          <div class="thumbnail-list">
            <div 
              v-for="pageNum in totalPages" 
              :key="pageNum"
              class="thumbnail-item"
              :class="{ active: pageNum === currentPage }"
              @click="goToPageNumber(pageNum)"
            >
              <div class="thumbnail-page">{{ pageNum }}</div>
            </div>
          </div>
        </div>

        <!-- 书签面板 -->
        <div v-else class="bookmarks-panel">
          <h3>书签</h3>
          <div class="bookmark-list">
            <div 
              v-for="bookmark in bookmarks" 
              :key="bookmark.id"
              class="bookmark-item"
              @click="goToBookmark(bookmark)"
            >
              <div class="bookmark-title">{{ bookmark.title }}</div>
              <div class="bookmark-page">第 {{ bookmark.pageNumber }} 页</div>
            </div>
          </div>
        </div>
      </div>

      <!-- PDF显示区域 -->
      <div class="pdf-viewer" ref="viewerContainer">
        <!-- 加载状态 -->
        <div v-if="isLoading" class="loading-container">
          <el-icon class="is-loading">
            <Loading />
          </el-icon>
          <span>正在加载PDF文档...</span>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="error" class="error-container">
          <el-icon>
            <WarningFilled />
          </el-icon>
          <span>{{ error }}</span>
          <el-button @click="retryLoad" type="primary" size="small">重试</el-button>
        </div>

        <!-- PDF组件 -->
        <div v-else-if="pdfDocument" class="pdf-container">
          <iframe
            :src="pdfDocument"
            class="pdf-iframe"
            @load="onPdfLoaded"
            @error="onPdfLoadingFailed"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  ArrowRight,
  ZoomIn,
  ZoomOut,
  Loading,
  WarningFilled,
  Search,
  Menu,
  Setting,
  Close
} from '@element-plus/icons-vue'
import { useReaderStore } from '../../store/reader'

// 导入URL polyfill（必须在@tato30/vue-pdf之前）
import '@renderer/utils/url-polyfill'

// PDF阅读器使用iframe实现



// Props
interface Props {
  /** 书籍ID */
  bookId: string
}

const props = defineProps<Props>()

// Emits
interface Emits {
  (e: 'go-back'): void
}

const emit = defineEmits<Emits>()

// 路由
const router = useRouter()

// 响应式数据
const isLoading = ref(true)
const error = ref<string>('')
const currentPage = ref(1)
const totalPages = ref(0)
const scale = ref(1.0)

const showSidebar = ref(false)
const sidebarMode = ref<'bookmarks' | 'thumbnails'>('bookmarks')
const bookmarks = ref<any[]>([])

// 新增功能状态
const showSearch = ref(false)
const showSettings = ref(false)
const pageInputValue = ref('')

// 搜索相关
const searchQuery = ref('')
const searchResults = ref<Array<{ text: string; page: number; position: number }>>([])
const currentSearchIndex = ref(-1)

// 设置相关
const displayMode = ref<'single' | 'continuous'>('single')
const autoShowSidebar = ref(false)
const rememberPosition = ref(true)
const autoSaveProgress = ref(true)

// PDF相关
const pdfFilePath = ref('')
const pdfDocument = ref()
const currentBlobUrl = ref('') // 用于跟踪当前的Blob URL以便清理



// PDF文件转换为Blob URL函数
const convertToFileUrl = async (filePath: string): Promise<string> => {
  console.log('原始文件路径:', filePath)

  if (filePath.startsWith('http') || filePath.startsWith('blob:')) {
    return filePath
  }

  try {
    // 通过IPC读取文件内容
    console.log('通过IPC读取PDF文件:', filePath)
    const fileBuffer = await window.electronAPI.file.read(filePath)

    // 将Buffer转换为Blob
    const blob = new Blob([fileBuffer], { type: 'application/pdf' })

    // 创建Blob URL
    const blobUrl = URL.createObjectURL(blob)

    console.log('转换后的Blob URL:', blobUrl)
    return blobUrl
  } catch (error) {
    console.error('读取PDF文件失败:', error)
    throw new Error(`无法读取PDF文件: ${error.message}`)
  }
}

// PDF加载状态监听
watch(pdfFilePath, async (newPath) => {
  console.log('pdfFilePath changed:', newPath)

  // 清理之前的Blob URL
  if (currentBlobUrl.value) {
    console.log('清理之前的Blob URL:', currentBlobUrl.value)
    URL.revokeObjectURL(currentBlobUrl.value)
    currentBlobUrl.value = ''
  }

  if (newPath) {
    try {
      isLoading.value = true
      error.value = ''
      console.log('PDF文档开始加载:', newPath)

      // 异步转换文件路径为Blob URL
      const pdfUrl = await convertToFileUrl(newPath)
      pdfDocument.value = pdfUrl
      currentBlobUrl.value = pdfUrl // 保存当前Blob URL用于后续清理

      // 设置一个超时，如果iframe没有触发load事件
      setTimeout(() => {
        if (isLoading.value) {
          console.log('PDF加载超时，但继续显示')
          isLoading.value = false
        }
      }, 5000) // 增加超时时间，因为文件读取可能需要更长时间

    } catch (err) {
      console.error('PDF加载错误:', err)
      error.value = `PDF加载失败: ${err.message || err}`
      isLoading.value = false
    }
  }
})



// 方法
const goBack = () => {
  emit('go-back')
}

const previousPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
    updateProgress()
  }
}

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
    updateProgress()
  }
}



const goToPageNumber = (pageNum: number) => {
  currentPage.value = pageNum
  updateProgress()
}

// 缩放功能
const zoomIn = () => {
  scale.value = Math.min(scale.value * 1.25, 5.0)
}

const zoomOut = () => {
  scale.value = Math.max(scale.value / 1.25, 0.25)
}

const resetZoom = () => {
  scale.value = 1.0
}

// 页面跳转功能
const goToPage = () => {
  const pageNum = parseInt(pageInputValue.value)
  if (pageNum && pageNum >= 1 && pageNum <= totalPages.value) {
    currentPage.value = pageNum
    updateProgress()
  }
  pageInputValue.value = '' // 清空输入框
}

// 搜索功能
const toggleSearch = () => {
  showSearch.value = !showSearch.value
  if (!showSearch.value) {
    // 关闭搜索时清空搜索结果
    searchQuery.value = ''
    searchResults.value = []
    currentSearchIndex.value = -1
  }
}

const performSearch = () => {
  if (!searchQuery.value.trim()) {
    searchResults.value = []
    return
  }

  // 尝试与iframe中的PDF查看器通信进行搜索
  const iframe = document.querySelector('.pdf-iframe') as HTMLIFrameElement
  if (iframe && iframe.contentWindow) {
    try {
      // 发送搜索命令到PDF查看器
      iframe.contentWindow.postMessage({
        type: 'search',
        query: searchQuery.value
      }, '*')

      // 模拟搜索结果（实际应该从PDF查看器接收）
      setTimeout(() => {
        searchResults.value = [
          { text: `包含"${searchQuery.value}"的文本片段`, page: 1, position: 0 },
          { text: `另一个包含"${searchQuery.value}"的片段`, page: 2, position: 100 }
        ]
      }, 500)

    } catch (error) {
      console.warn('无法与PDF查看器通信:', error)
      ElMessage.warning('搜索功能需要浏览器PDF查看器支持')
    }
  } else {
    ElMessage.info('PDF文档未加载完成，请稍后再试')
  }
}

const goToSearchResult = (result: { text: string; page: number; position: number }) => {
  // 跳转到搜索结果所在页面
  currentPage.value = result.page
  updateProgress()
  console.log('跳转到搜索结果:', result)
}

// 设置功能
const toggleSettings = () => {
  showSettings.value = !showSettings.value
}

const toggleSidebar = () => {
  showSidebar.value = !showSidebar.value
}

const handleSidebarCommand = (command: string) => {
  switch (command) {
    case 'toggle':
      toggleSidebar()
      break
    case 'thumbnails':
      sidebarMode.value = 'thumbnails'
      if (!showSidebar.value) {
        showSidebar.value = true
      }
      break
    case 'bookmarks':
      sidebarMode.value = 'bookmarks'
      if (!showSidebar.value) {
        showSidebar.value = true
      }
      break
  }
}



// 注意：由于使用iframe方案，大部分高级功能已移除
// 用户可以通过浏览器的PDF查看器进行操作



const onPdfLoaded = (event: any) => {
  console.log('PDF iframe加载完成:', event)
  isLoading.value = false

  // 由于使用iframe，我们无法直接获取页数，设置一个默认值
  if (totalPages.value === 0) {
    totalPages.value = 1 // 默认至少有1页
  }

  // 恢复阅读进度
  restoreReadingProgress()
}

const onPdfLoadingFailed = (error: any) => {
  console.error('PDF iframe加载失败:', error)
  error.value = `PDF加载失败: 无法显示PDF文件`
  isLoading.value = false
}

const updateProgress = async () => {
  if (window.electronAPI?.reader?.updateProgress) {
    const progress = (currentPage.value / totalPages.value) * 100
    await window.electronAPI.reader.updateProgress(props.bookId, progress, currentPage.value)
  }
}

const retryLoad = () => {
  error.value = ''
  loadPdfDocument()
}

const restoreReadingProgress = async () => {
  try {
    // 获取阅读进度
    const progress = await window.electronAPI.reader.getProgress(props.bookId)
    if (progress && progress.currentPage) {
      currentPage.value = progress.currentPage
    }
  } catch (error) {
    console.error('恢复阅读进度失败:', error)
  }
}

const loadPdfDocument = async () => {
  try {
    console.log('开始加载PDF文档，书籍ID:', props.bookId)
    isLoading.value = true
    error.value = ''

    // 获取书籍信息
    const book = await window.electronAPI.reader.getBook(props.bookId)
    console.log('获取到书籍信息:', book)

    if (!book) {
      throw new Error('书籍不存在')
    }

    // 设置PDF文件路径
    const filePath = book.file_path || book.filePath
    console.log('设置PDF文件路径:', filePath)

    if (!filePath) {
      throw new Error('PDF文件路径为空')
    }

    pdfFilePath.value = filePath

    // 加载书签
    await loadBookmarks()

    console.log('PDF文档路径设置完成:', pdfFilePath.value)

  } catch (err) {
    console.error('加载PDF失败:', err)
    error.value = `加载失败: ${err.message || err}`
    isLoading.value = false
  }
}

const loadBookmarks = async () => {
  // 书签功能已删除，不再加载书签
  console.log('书签功能已禁用')
}

// 键盘快捷键处理
const handleKeydown = (event: KeyboardEvent) => {
  // 防止在输入框中触发快捷键
  if (event.target instanceof HTMLInputElement) {
    return
  }

  switch (event.key) {
    case 'ArrowLeft':
    case 'PageUp':
      event.preventDefault()
      previousPage()
      break
    case 'ArrowRight':
    case 'PageDown':
    case ' ': // 空格键
      event.preventDefault()
      nextPage()
      break
    case '+':
    case '=':
      event.preventDefault()
      zoomIn()
      break
    case '-':
      event.preventDefault()
      zoomOut()
      break
    case '0':
      event.preventDefault()
      resetZoom()
      break
    case 'f':
    case 'F':
      if (event.ctrlKey || event.metaKey) {
        event.preventDefault()
        toggleSearch()
      }
      break
    case 'Escape':
      if (showSearch.value) {
        event.preventDefault()
        toggleSearch()
      } else if (showSettings.value) {
        event.preventDefault()
        toggleSettings()
      }
      break
    case 's':
    case 'S':
      if (event.ctrlKey || event.metaKey) {
        event.preventDefault()
        toggleSidebar()
      }
      break
  }
}

// Store
const readerStore = useReaderStore()

// 计算属性
const {
  currentBook
} = readerStore

// 生命周期
onMounted(() => {
  loadPdfDocument()
  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  // 清理资源
  document.removeEventListener('keydown', handleKeydown)

  // 清理Blob URL
  if (currentBlobUrl.value) {
    console.log('组件卸载时清理Blob URL:', currentBlobUrl.value)
    URL.revokeObjectURL(currentBlobUrl.value)
    currentBlobUrl.value = ''
  }
})
</script>

<style scoped>
.pdf-reader-view {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f5f5f5;
}

.toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  background: white;
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  min-height: 60px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.book-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  margin: 0 20px;
  min-width: 0;
}

.book-title {
  font-weight: 600;
  font-size: 16px;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 300px;
}

.reading-progress {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

.page-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 8px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background: #f9f9f9;
}

.page-info {
  display: flex;
  align-items: center;
  gap: 4px;
}

.page-input {
  width: 60px;
}

.page-input :deep(.el-input__inner) {
  text-align: center;
  padding: 0 8px;
}

.page-total {
  font-size: 14px;
  color: #666;
  white-space: nowrap;
}

.zoom-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 8px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background: #f9f9f9;
}

.zoom-level {
  font-size: 12px;
  color: #666;
  min-width: 40px;
  text-align: center;
}


/* 搜索面板样式 */
.search-panel {
  position: absolute;
  top: 70px;
  right: 20px;
  width: 350px;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
}

.search-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.search-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.search-content {
  padding: 16px;
}

.search-results {
  margin-top: 16px;
}

.search-stats {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.search-list {
  max-height: 300px;
  overflow-y: auto;
}

.search-item {
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  margin-bottom: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.search-item:hover {
  background-color: #f5f5f5;
}

.search-text {
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
  line-height: 1.4;
}

.search-page {
  font-size: 12px;
  color: #666;
}

/* 设置面板样式 */
.settings-container {
  padding: 20px;
}

.setting-group {
  margin-bottom: 24px;
}

.setting-group h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.setting-item {
  margin-bottom: 16px;
}

.setting-item label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: #666;
}

/* 快捷键说明样式 */
.shortcut-list {
  background: #f9f9f9;
  border-radius: 6px;
  padding: 12px;
}

.shortcut-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
  border-bottom: 1px solid #e0e0e0;
}

.shortcut-item:last-child {
  border-bottom: none;
}

.shortcut-key {
  font-family: 'Courier New', monospace;
  background: #e0e0e0;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
  font-weight: bold;
}

.shortcut-desc {
  font-size: 13px;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .toolbar-right {
    gap: 8px;
  }

  .page-controls,
  .zoom-controls {
    padding: 0 6px;
  }

  .book-info {
    margin: 0 15px;
  }

  .book-title {
    max-width: 200px;
  }
}

@media (max-width: 900px) {
  .toolbar {
    padding: 6px 12px;
    min-height: 50px;
  }

  .toolbar-right {
    gap: 6px;
  }

  .page-controls,
  .zoom-controls {
    padding: 0 4px;
    gap: 4px;
  }

  .book-info {
    margin: 0 10px;
  }

  .book-title {
    font-size: 14px;
    max-width: 150px;
  }

  .reading-progress {
    font-size: 11px;
  }

  .zoom-level {
    font-size: 11px;
    min-width: 35px;
  }

  .page-input {
    width: 50px;
  }

  .search-panel {
    width: 300px;
    right: 10px;
  }
}

@media (max-width: 600px) {
  .toolbar {
    flex-wrap: wrap;
    min-height: auto;
  }

  .toolbar-left {
    order: 1;
  }

  .book-info {
    order: 2;
    width: 100%;
    margin: 8px 0 4px 0;
    text-align: center;
  }

  .toolbar-right {
    order: 3;
    width: 100%;
    justify-content: center;
    margin-top: 4px;
  }

  .search-panel {
    width: calc(100vw - 20px);
    right: 10px;
    left: 10px;
  }
}

.content-area {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.sidebar {
  width: 300px;
  background: white;
  border-right: 1px solid #e0e0e0;
  overflow-y: auto;
}

.thumbnails-panel,
.bookmarks-panel {
  padding: 16px;
}

.thumbnails-panel h3,
.bookmarks-panel h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.thumbnail-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 8px;
}

.thumbnail-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  border: 2px solid transparent;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.thumbnail-item:hover {
  background: #f0f0f0;
}

.thumbnail-item.active {
  border-color: #409eff;
  background: #e6f7ff;
}

.thumbnail-page {
  width: 60px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 12px;
  color: #666;
}

.bookmark-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.bookmark-item {
  padding: 12px;
  background: #f9f9f9;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.2s;
}

.bookmark-item:hover {
  background: #e6f7ff;
}

.bookmark-title {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.bookmark-page {
  font-size: 12px;
  color: #666;
}

.pdf-viewer {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: auto;
  background: #e5e5e5;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 40px;
  color: #666;
}

.loading-container .el-icon {
  font-size: 32px;
}

.error-container .el-icon {
  font-size: 32px;
  color: #f56c6c;
}

.pdf-container {
  display: flex;
  justify-content: center;
  padding: 10px;
  width: 100%;
  height: 100%;
}

.pdf-iframe {
  width: 100%;
  height: 100%;
  border: none;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: white;
  transition: transform 0.2s ease;
  max-width: 100%;
  max-height: 100%;
}

.pdf-iframe:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

/* PDF文本层样式 */
:deep(.textLayer) {
  opacity: 0.2;
}

:deep(.textLayer:hover) {
  opacity: 1;
}

/* PDF高亮样式 */
:deep(.highlight) {
  background-color: rgba(255, 255, 0, 0.3);
  border-radius: 2px;
}

/* PDF注释层样式 */
:deep(.annotationLayer) {
  opacity: 0.9;
}

/* 滚动条样式 */
.pdf-viewer::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.pdf-viewer::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.pdf-viewer::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.pdf-viewer::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .toolbar {
    flex-wrap: wrap;
    gap: 8px;
  }

  .toolbar-center {
    order: 3;
    width: 100%;
    justify-content: center;
  }

  .sidebar {
    width: 250px;
  }

  .thumbnail-list {
    grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
  }
}
</style>
