/**
 * 服务管理器
 * 统一管理所有服务和IPC处理器的初始化
 */

import { DatabaseManager } from '../database-manager'
import { FileManager } from '../file-manager'
import { WindowManager } from '../window-manager'
import { BookService } from './BookService'
import { BookIPCHandler } from '../ipc-handlers/BookIPCHandler'
import { TxtReaderIPCHandler } from '../ipc-handlers/TxtReaderIPCHandler'
import { EpubReaderIPCHandler } from '../ipc-handlers/EpubReaderIPCHandler'
import { ReaderIPCHandler } from '../ipc-handlers/ReaderIPCHandler'
import { ipcMain } from 'electron'

export class ServiceManager {
  private databaseManager: DatabaseManager
  private fileManager: FileManager
  private windowManager: WindowManager
  private bookService: BookService
  private bookIPCHandler: BookIPCHandler
  private txtReaderIPCHandler: TxtReaderIPCHandler
  private epubReaderIPCHandler: EpubReaderIPCHandler
  private readerIPCHandler: ReaderIPCHandler

  constructor(windowManager: WindowManager) {
    // 初始化核心服务
    this.databaseManager = new DatabaseManager()
    this.fileManager = new FileManager()
    this.windowManager = windowManager

    // 初始化业务服务
    this.bookService = new BookService(this.databaseManager, this.fileManager)

    // 初始化IPC处理器
    this.bookIPCHandler = new BookIPCHandler(this.bookService)
    this.txtReaderIPCHandler = new TxtReaderIPCHandler()
    this.epubReaderIPCHandler = new EpubReaderIPCHandler()
    this.readerIPCHandler = new ReaderIPCHandler(this.bookService)
  }

  /**
   * 初始化所有服务
   */
  async initialize(): Promise<void> {
    try {
      console.log('ServiceManager: 开始初始化服务...')
      
      // 初始化数据库
      await this.databaseManager.initialize()
      console.log('ServiceManager: 数据库初始化完成')
      
      // 初始化文件管理器
      await this.fileManager.initialize()
      console.log('ServiceManager: 文件管理器初始化完成')
      
      // 注册IPC处理器
      this.bookIPCHandler.registerHandlers()
      this.txtReaderIPCHandler.registerHandlers()
      this.epubReaderIPCHandler.registerHandlers()
      this.readerIPCHandler.registerHandlers()
      this.registerBookmarkHandlers()
      this.registerFileHandlers()
      this.registerWindowHandlers()
      console.log('ServiceManager: IPC处理器注册完成')
      
      console.log('ServiceManager: 所有服务初始化完成')
    } catch (error) {
      console.error('ServiceManager: 服务初始化失败:', error)
      throw error
    }
  }

  /**
   * 清理所有服务
   */
  async cleanup(): Promise<void> {
    try {
      console.log('ServiceManager: 开始清理服务...')
      
      // 注销IPC处理器
      this.bookIPCHandler.unregisterHandlers()
      this.txtReaderIPCHandler.unregisterHandlers()
      this.epubReaderIPCHandler.unregisterHandlers()
      this.readerIPCHandler.unregisterHandlers()
      this.unregisterBookmarkHandlers()
      this.unregisterFileHandlers()
      this.unregisterWindowHandlers()
      console.log('ServiceManager: IPC处理器注销完成')
      
      // 清理数据库连接
      await this.databaseManager.close()
      console.log('ServiceManager: 数据库连接关闭完成')
      
      console.log('ServiceManager: 所有服务清理完成')
    } catch (error) {
      console.error('ServiceManager: 服务清理失败:', error)
      throw error
    }
  }

  /**
   * 注册书签相关的IPC处理器
   */
  private registerBookmarkHandlers(): void {
    // 添加书签
    ipcMain.handle('bookmark:add', async (_, bookmark: any): Promise<any> => {
      try {
        const db = this.databaseManager.getDatabase()
        const bookmarkId = require('crypto').randomUUID()
        const now = new Date()

        const newBookmark = {
          ...bookmark,
          id: bookmarkId,
          createdAt: now,
          updatedAt: now
        }

        const stmt = db.prepare(`
          INSERT INTO bookmarks (id, book_id, title, content, position, created_at, updated_at)
          VALUES (?, ?, ?, ?, ?, ?, ?)
        `)

        stmt.run(
          newBookmark.id,
          newBookmark.bookId,
          newBookmark.title,
          newBookmark.content || '',
          newBookmark.position,
          newBookmark.createdAt.toISOString(),
          newBookmark.updatedAt.toISOString()
        )

        console.log('ServiceManager: 书签添加成功', newBookmark.id)
        return newBookmark
      } catch (error) {
        console.error('ServiceManager: 添加书签失败:', error)
        throw error
      }
    })

    // 删除书签
    ipcMain.handle('bookmark:remove', async (_, bookmarkId: string): Promise<boolean> => {
      try {
        const db = this.databaseManager.getDatabase()
        const stmt = db.prepare('DELETE FROM bookmarks WHERE id = ?')
        const result = stmt.run(bookmarkId)
        console.log('ServiceManager: 书签删除成功', bookmarkId)
        return result.changes > 0
      } catch (error) {
        console.error('ServiceManager: 删除书签失败:', error)
        throw error
      }
    })

    // 获取书签列表
    ipcMain.handle('bookmark:list', async (_, bookId: string): Promise<any[]> => {
      try {
        const db = this.databaseManager.getDatabase()
        const stmt = db.prepare(`
          SELECT * FROM bookmarks
          WHERE book_id = ? AND deleted_at IS NULL
          ORDER BY created_at DESC
        `)
        const rows = stmt.all(bookId) as any[]

        const bookmarks = rows.map(row => ({
          ...row,
          createdAt: new Date(row.created_at),
          updatedAt: new Date(row.updated_at)
        }))

        console.log(`ServiceManager: 获取书签列表成功，共 ${bookmarks.length} 个书签`)
        return bookmarks
      } catch (error) {
        console.error('ServiceManager: 获取书签列表失败:', error)
        throw error
      }
    })

    console.log('ServiceManager: 书签IPC处理器注册完成')
  }

  /**
   * 注册文件相关的IPC处理器
   */
  private registerFileHandlers(): void {
    // 读取文件
    ipcMain.handle('file:read', async (_, filePath: string): Promise<Buffer> => {
      return this.fileManager.readFile(filePath)
    })

    // 读取文件为base64
    ipcMain.handle('file:read-as-base64', async (_, filePath: string): Promise<string> => {
      return this.fileManager.readFileAsBase64(filePath)
    })

    // 检查文件是否存在
    ipcMain.handle('file:exists', async (_, filePath: string): Promise<boolean> => {
      return this.fileManager.fileExists(filePath)
    })

    // 选择文件
    ipcMain.handle('file:select', async (_, filters?: Electron.FileFilter[]): Promise<string[]> => {
      return this.fileManager.selectFiles(filters)
    })

    // 获取文件信息
    ipcMain.handle('file:get-info', async (_, filePath: string): Promise<{
      size: number
      format: string
      name: string
      hash: string
    }> => {
      return this.fileManager.getFileInfo(filePath)
    })

    // 选择图片文件
    ipcMain.handle('file:select-image', async (): Promise<string | null> => {
      return this.fileManager.selectImageFile()
    })

    // 验证图片文件
    ipcMain.handle('file:validate-image', async (_, filePath: string): Promise<{
      isValid: boolean
      error?: string
      info?: {
        size: number
        type: string
      }
    }> => {
      return this.fileManager.validateImageFile(filePath)
    })

    console.log('ServiceManager: 文件IPC处理器注册完成')
  }

  /**
   * 注销书签相关的IPC处理器
   */
  private unregisterBookmarkHandlers(): void {
    const handlers = ['bookmark:add', 'bookmark:remove', 'bookmark:list']
    handlers.forEach(handler => {
      ipcMain.removeAllListeners(handler)
    })
    console.log('ServiceManager: 书签IPC处理器注销完成')
  }

  /**
   * 注销文件相关的IPC处理器
   */
  private unregisterFileHandlers(): void {
    const handlers = ['file:read', 'file:read-as-base64', 'file:exists', 'file:select']
    handlers.forEach(handler => {
      ipcMain.removeAllListeners(handler)
    })
    console.log('ServiceManager: 文件IPC处理器注销完成')
  }

  /**
   * 注册窗口相关的IPC处理器
   */
  private registerWindowHandlers(): void {
    // 最小化窗口
    ipcMain.handle('window:minimize', () => {
      this.windowManager.minimizeWindow()
    })

    // 最大化窗口
    ipcMain.handle('window:maximize', () => {
      this.windowManager.toggleMaximizeWindow()
    })

    // 关闭窗口
    ipcMain.handle('window:close', () => {
      this.windowManager.closeWindow()
    })

    // 切换全屏
    ipcMain.handle('window:toggle-fullscreen', () => {
      this.windowManager.toggleFullscreen()
    })

    console.log('ServiceManager: 窗口IPC处理器注册完成')
  }

  /**
   * 注销窗口相关的IPC处理器
   */
  private unregisterWindowHandlers(): void {
    const handlers = [
      'window:minimize',
      'window:maximize',
      'window:close',
      'window:toggle-fullscreen'
    ]

    handlers.forEach(handler => {
      ipcMain.removeAllListeners(handler)
    })

    console.log('ServiceManager: 窗口IPC处理器注销完成')
  }

  /**
   * 获取服务实例
   */
  getServices() {
    return {
      databaseManager: this.databaseManager,
      fileManager: this.fileManager,
      windowManager: this.windowManager,
      bookService: this.bookService
    }
  }
}
