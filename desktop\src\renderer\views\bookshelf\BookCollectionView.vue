<!--
  图书收藏页面
  专门管理收藏的图书，支持搜索、筛选、排序等功能
-->
<template>
  <div class="collection-view">
    <!-- 页面头部 -->
    <div class="collection-header">
      <div class="header-left">
        <h1 class="page-title">图书收藏</h1>
        <div class="stats-info">
          <span>共收藏 {{ stats.total }} 本图书</span>
          <span v-if="stats.filtered !== stats.total">，筛选后 {{ stats.filtered }} 本</span>
        </div>
      </div>

      <div class="header-actions">
        <!-- 搜索框 -->
        <el-input
          :model-value="searchQuery"
          @update:model-value="handleSearch"
          placeholder="搜索收藏的图书标题、作者..."
          class="search-input"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>

        <!-- 筛选控件 -->
        <div class="filter-controls">
          <!-- 格式筛选 -->
          <el-select
            :model-value="selectedFormat"
            @update:model-value="handleFormatChange"
            placeholder="全部格式"
            style="width: 120px"
          >
            <el-option label="全部格式" value="all" />
            <el-option label="EPUB" value="epub" />
            <el-option label="PDF" value="pdf" />
            <el-option label="TXT" value="txt" />
            <el-option label="MOBI" value="mobi" />
            <el-option label="AZW3" value="azw3" />
          </el-select>

          <!-- 作者筛选 -->
          <el-select
            :model-value="selectedAuthor"
            @update:model-value="handleAuthorChange"
            placeholder="全部作者"
            style="width: 120px"
            filterable
            clearable
          >
            <el-option label="全部作者" value="" />
            <el-option
              v-for="author in uniqueAuthors"
              :key="author"
              :label="author"
              :value="author"
            />
          </el-select>

          <!-- 清除筛选 -->
          <el-button
            v-if="hasActiveFilters"
            @click="clearFilters"
            size="small"
            type="info"
            plain
          >
            <el-icon><Close /></el-icon>
            清除筛选
          </el-button>
        </div>

        <!-- 操作按钮组 -->
        <div class="action-controls">
          <!-- 排序选择 -->
          <el-select
            :model-value="sortField"
            @update:model-value="handleSortChange"
            placeholder="排序方式"
            style="width: 120px"
          >
            <el-option label="收藏时间" value="updatedAt" />
            <el-option label="最后阅读" value="lastReadAt" />
            <el-option label="书名" value="title" />
            <el-option label="作者" value="author" />
            <el-option label="阅读进度" value="readProgress" />
          </el-select>

          <!-- 排序方向 -->
          <el-button @click="toggleSortOrder">
            <el-icon>
              <component :is="sortOrder === 'asc' ? SortUp : SortDown" />
            </el-icon>
            <span>{{ sortOrder === 'asc' ? '升序' : '降序' }}</span>
          </el-button>

          <!-- 视图切换 -->
          <el-button-group>
            <el-button
              :type="viewMode === 'grid' ? 'primary' : 'default'"
              @click="setViewMode('grid')"
            >
              <el-icon><Grid /></el-icon>
            </el-button>
            <el-button
              :type="viewMode === 'list' ? 'primary' : 'default'"
              @click="setViewMode('list')"
            >
              <el-icon><List /></el-icon>
            </el-button>
          </el-button-group>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="collection-content">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="6" animated />
      </div>

      <!-- 空状态 -->
      <div v-else-if="filteredBooks.length === 0" class="empty-state">
        <el-empty :description="searchQuery || hasActiveFilters ? '没有找到匹配的收藏图书' : '还没有收藏任何图书'">
          <el-button type="primary" @click="goToLibrary">
            去图书列表收藏
          </el-button>
        </el-empty>
      </div>

      <!-- 网格视图 -->
      <div v-else-if="viewMode === 'grid'" class="books-grid">
        <div
          v-for="book in paginatedBooks"
          :key="book.id"
          class="book-card"
          @click="handleBookClick(book)"
        >
          <!-- 收藏标记 -->
          <el-icon class="favorite-icon" color="#f56c6c">
            <Star />
          </el-icon>

          <div class="book-cover">
            <img
              :src="book.coverPath || placeholderImage"
              :alt="book.title"
              @error="handleImageError"
            />
            <div class="book-overlay">
              <el-button type="primary" size="small">
                <el-icon><View /></el-icon>
                打开
              </el-button>
            </div>

            <!-- 阅读进度 -->
            <div v-if="book.readProgress > 0" class="progress-bar">
              <div
                class="progress-fill"
                :style="{ width: book.readProgress + '%' }"
              />
            </div>
          </div>

          <div class="book-info">
            <h3 :title="book.title">{{ book.title }}</h3>
            <p :title="book.author">{{ book.author }}</p>
            <div class="book-meta">
              <span class="file-type">{{ book.format.toUpperCase() }}</span>
              <span class="file-size">{{ formatFileSize(book.fileSize) }}</span>
            </div>
            <div v-if="book.readProgress > 0" class="read-progress">
              已读 {{ book.readProgress }}%
            </div>
          </div>

          <!-- 快捷操作 -->
          <div class="book-actions">
            <el-dropdown trigger="click" @click.stop>
              <el-button size="small" circle>
                <el-icon><MoreFilled /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="handleBookDetail(book)">
                    <el-icon><InfoFilled /></el-icon>
                    详情
                  </el-dropdown-item>
                  <el-dropdown-item @click="removeFavorite(book.id)">
                    <el-icon><Star /></el-icon>
                    取消收藏
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleBookEdit(book)">
                    <el-icon><Edit /></el-icon>
                    编辑
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>

      <!-- 列表视图 -->
      <div v-else class="books-list">
        <el-table
          :data="paginatedBooks"
          style="width: 100%"
          @row-click="handleBookClick"
          row-class-name="book-row"
        >
          <!-- 封面列 -->
          <el-table-column label="封面" width="80">
            <template #default="{ row }">
              <div class="table-cover">
                <img
                  :src="row.coverPath || placeholderImage"
                  :alt="row.title"
                  @error="handleImageError"
                />
              </div>
            </template>
          </el-table-column>

          <!-- 图书信息列 -->
          <el-table-column label="图书信息" min-width="200">
            <template #default="{ row }">
              <div class="book-title">{{ row.title }}</div>
              <div class="book-author">{{ row.author }}</div>
            </template>
          </el-table-column>

          <!-- 格式列 -->
          <el-table-column label="格式" width="80">
            <template #default="{ row }">
              <el-tag size="small">{{ row.format.toUpperCase() }}</el-tag>
            </template>
          </el-table-column>

          <!-- 阅读进度列 -->
          <el-table-column label="阅读进度" width="120">
            <template #default="{ row }">
              <el-progress
                :percentage="row.readProgress"
                :show-text="false"
                :stroke-width="6"
              />
              <span class="progress-text">{{ row.readProgress }}%</span>
            </template>
          </el-table-column>

          <!-- 收藏时间列 -->
          <el-table-column label="收藏时间" width="120">
            <template #default="{ row }">
              {{ formatDate(row.updatedAt) }}
            </template>
          </el-table-column>

          <!-- 操作列 -->
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click.stop="handleBookClick(row)">
                打开
              </el-button>
              <el-dropdown trigger="click" @click.stop>
                <el-button size="small" circle>
                  <el-icon><MoreFilled /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="handleBookDetail(row)">详情</el-dropdown-item>
                    <el-dropdown-item @click="removeFavorite(row.id)">取消收藏</el-dropdown-item>
                    <el-dropdown-item @click="handleBookEdit(row)">编辑</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div v-if="paginatedBooks.length > 0" class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :total="filteredBooks.length"
          :page-sizes="[12, 24, 48, 96]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePageSizeChange"
          @current-change="handleCurrentPageChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search, Grid, List, SortUp, SortDown, Star, View, MoreFilled,
  InfoFilled, Edit, Close
} from '@element-plus/icons-vue'
import placeholderImage from '@renderer/assets/placeholder-book.svg'
import { useUnifiedLibraryStore } from '@renderer/store/unifiedLibrary'
import type { BookInfo } from '@shared/types'

// 路由和状态管理
const router = useRouter()
const libraryStore = useUnifiedLibraryStore()

// 从store中解构响应式状态（使用 storeToRefs 保持响应性）
const { books, loading, error } = storeToRefs(libraryStore)

// 从store中解构方法（方法不需要 storeToRefs）
const { setFavorite, loadBooks } = libraryStore

// 本地状态
const searchQuery = ref('')
const selectedFormat = ref('all')
const selectedAuthor = ref('')
const sortField = ref<'updatedAt' | 'lastReadAt' | 'title' | 'author' | 'readProgress'>('updatedAt')
const sortOrder = ref<'asc' | 'desc'>('desc')
const viewMode = ref<'grid' | 'list'>('grid')
const currentPage = ref(1)
const pageSize = ref(24)

// 计算属性 - 获取收藏的图书
const favoriteBooks = computed(() => {
  return books.value?.filter(book => book.isFavorite) || []
})

// 计算属性 - 筛选后的图书
const filteredBooks = computed(() => {
  let result = favoriteBooks.value || []

  // 搜索筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(book =>
      book.title.toLowerCase().includes(query) ||
      (book.author && book.author.toLowerCase().includes(query))
    )
  }

  // 格式筛选
  if (selectedFormat.value !== 'all') {
    result = result.filter(book => book.format === selectedFormat.value)
  }

  // 作者筛选
  if (selectedAuthor.value) {
    result = result.filter(book => book.author === selectedAuthor.value)
  }

  return result
})

// 计算属性 - 排序后的图书
const sortedBooks = computed(() => {
  const books = [...(filteredBooks.value || [])]

  books.sort((a, b) => {
    let aValue: any = a[sortField.value]
    let bValue: any = b[sortField.value]

    // 处理日期字段
    if (sortField.value === 'updatedAt' || sortField.value === 'lastReadAt') {
      aValue = aValue ? new Date(aValue).getTime() : 0
      bValue = bValue ? new Date(bValue).getTime() : 0
    }

    // 处理字符串字段
    if (typeof aValue === 'string') {
      aValue = aValue.toLowerCase()
      bValue = bValue.toLowerCase()
    }

    if (sortOrder.value === 'asc') {
      return aValue > bValue ? 1 : -1
    } else {
      return aValue < bValue ? 1 : -1
    }
  })

  return books
})

// 计算属性 - 分页后的图书
const paginatedBooks = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return (sortedBooks.value || []).slice(start, end)
})

// 计算属性 - 统计信息
const stats = computed(() => ({
  total: favoriteBooks.value?.length || 0,
  filtered: filteredBooks.value?.length || 0
}))

// 计算属性 - 是否有活跃的筛选条件
const hasActiveFilters = computed(() => {
  return searchQuery.value || selectedFormat.value !== 'all' || selectedAuthor.value
})

// 计算属性 - 获取所有唯一的作者
const uniqueAuthors = computed(() => {
  const authors = new Set<string>()
  favoriteBooks.value?.forEach(book => {
    if (book.author && book.author.trim()) {
      authors.add(book.author.trim())
    }
  })
  return Array.from(authors).sort()
})

// 方法 - 搜索处理
const handleSearch = (query: string) => {
  searchQuery.value = query
  currentPage.value = 1
}

// 方法 - 格式筛选处理
const handleFormatChange = (format: string) => {
  selectedFormat.value = format
  currentPage.value = 1
}

// 方法 - 作者筛选处理
const handleAuthorChange = (author: string) => {
  selectedAuthor.value = author
  currentPage.value = 1
}

// 方法 - 清除筛选
const clearFilters = () => {
  searchQuery.value = ''
  selectedFormat.value = 'all'
  selectedAuthor.value = ''
  currentPage.value = 1
}

// 方法 - 排序处理
const handleSortChange = (field: typeof sortField.value) => {
  sortField.value = field
  currentPage.value = 1
}

// 方法 - 切换排序方向
const toggleSortOrder = () => {
  sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
  currentPage.value = 1
}

// 方法 - 设置视图模式
const setViewMode = (mode: 'grid' | 'list') => {
  viewMode.value = mode
}

// 方法 - 分页处理
const handlePageSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentPageChange = (page: number) => {
  currentPage.value = page
}

// 方法 - 图书点击处理
const handleBookClick = (book: BookInfo) => {
  router.push(`/reader/${book.id}`)
}

// 方法 - 取消收藏
const removeFavorite = async (bookId: string) => {
  try {
    await ElMessageBox.confirm('确定要取消收藏这本图书吗？', '确认操作', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await setFavorite(bookId, false)
    ElMessage.success('已取消收藏')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
      console.error('取消收藏失败:', error)
    }
  }
}

// 方法 - 图书详情
const handleBookDetail = (book: BookInfo) => {
  // TODO: 打开图书详情对话框
  console.log('查看图书详情:', book)
}

// 方法 - 编辑图书
const handleBookEdit = (book: BookInfo) => {
  // TODO: 打开图书编辑对话框
  console.log('编辑图书:', book)
}

// 方法 - 跳转到图书列表
const goToLibrary = () => {
  router.push('/library')
}

// 方法 - 图片加载错误处理
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = placeholderImage
}

// 方法 - 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 方法 - 格式化日期
const formatDate = (dateString: string): string => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

// 生命周期 - 组件挂载时加载数据
onMounted(async () => {
  try {
    await libraryStore.loadBooks()
  } catch (err) {
    console.error('加载图书列表失败:', err)
  }
})
</script>

<style scoped>
/* 页面容器 */
.collection-view {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color-page);
}

/* 页面头部 */
.collection-header {
  padding: 20px;
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
  flex-wrap: wrap;
}

.header-left {
  flex: 1;
  min-width: 200px;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.stats-info {
  color: var(--el-text-color-regular);
  font-size: 14px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.search-input {
  width: 300px;
}

.filter-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 主要内容区域 */
.collection-content {
  flex: 1;
  padding: 20px;
  overflow: auto;
}

.loading-container {
  padding: 40px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

/* 网格视图样式 */
.books-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
  padding: 20px 0;
}

.book-card {
  position: relative;
  background: var(--el-bg-color);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.book-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.favorite-icon {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 2;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  padding: 4px;
  font-size: 16px;
}

.book-cover {
  position: relative;
  width: 100%;
  height: 240px;
  overflow: hidden;
}

.book-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.book-card:hover .book-cover img {
  transform: scale(1.05);
}

.book-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.book-card:hover .book-overlay {
  opacity: 1;
}

.progress-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: rgba(0, 0, 0, 0.2);
}

.progress-fill {
  height: 100%;
  background: var(--el-color-primary);
  transition: width 0.3s ease;
}

.book-info {
  padding: 16px;
}

.book-info h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.book-info p {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: var(--el-text-color-regular);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.book-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.file-type {
  background: var(--el-color-primary);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.file-size {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.read-progress {
  font-size: 12px;
  color: var(--el-color-primary);
  font-weight: 500;
}

.book-actions {
  position: absolute;
  top: 8px;
  left: 8px;
  z-index: 2;
}

/* 列表视图样式 */
.books-list {
  background: var(--el-bg-color);
  border-radius: 8px;
  overflow: hidden;
}

.table-cover {
  width: 50px;
  height: 70px;
  border-radius: 4px;
  overflow: hidden;
}

.table-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.book-title {
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.book-author {
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.progress-text {
  margin-left: 8px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

/* 分页容器 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid var(--el-border-color-lighter);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .collection-header {
    flex-direction: column;
    align-items: stretch;
  }

  .header-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .search-input {
    width: 100%;
  }

  .filter-controls,
  .action-controls {
    justify-content: space-between;
  }

  .books-grid {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .collection-header {
    padding: 16px;
  }

  .collection-content {
    padding: 16px;
  }

  .books-grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 12px;
  }
}
</style>
