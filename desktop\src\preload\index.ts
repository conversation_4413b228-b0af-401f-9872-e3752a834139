/**
 * 预加载脚本
 * 在渲染进程中暴露安全的API接口
 */

import { contextBridge, ipc<PERSON>enderer } from 'electron'
import type { BookInfo } from '@shared/types'
// import type { IPCEvents } from '@shared/types'

// 定义暴露给渲染进程的API接口
export interface ElectronAPI {
  // 书籍管理
  book: {
    add: (filePath: string) => Promise<any>
    addWithDetails: (bookData: any) => Promise<any>
    extractMetadata: (filePath: string) => Promise<any>
    remove: (bookId: string) => Promise<boolean>
    list: () => Promise<any[]>
    get: (bookId: string) => Promise<any | null>
    updateProgress: (bookId: string, progress: number, currentPage?: number) => Promise<boolean>
    updateInfo: (bookId: string, bookData: Partial<BookInfo>) => Promise<boolean>
    setFavorite: (bookId: string, isFavorite: boolean) => Promise<boolean>
    getCover: (coverPath: string) => Promise<string | null>
  }

  // 阅读器专用API
  reader: {
    getBook: (bookId: string) => Promise<BookInfo | null>
    getProgress: (bookId: string) => Promise<{ currentPage?: number; progress?: number } | null>
    updateProgress: (bookId: string, progress: number, currentPage?: number) => Promise<boolean>
    checkFile: (filePath: string) => Promise<boolean>
    getBooks: () => Promise<BookInfo[]>
  }

  // 书签管理
  bookmark: {
    add: (bookmark: any) => Promise<any>
    remove: (bookmarkId: string) => Promise<boolean>
    list: (bookId: string) => Promise<any[]>
    update: (bookmarkId: string, updates: any) => Promise<boolean>
  }

  // 笔记管理
  note: {
    add: (note: any) => Promise<any>
    remove: (noteId: string) => Promise<boolean>
    list: (bookId: string) => Promise<any[]>
    update: (noteId: string, updates: any) => Promise<boolean>
  }

  // 设置管理
  settings: {
    get: () => Promise<any>
    update: (settings: any) => Promise<boolean>
  }

  // 文件操作
  file: {
    read: (filePath: string) => Promise<Buffer>
    readAsBase64: (filePath: string) => Promise<string>
    exists: (filePath: string) => Promise<boolean>
    select: (filters?: Electron.FileFilter[]) => Promise<string[]>
  }

  // TXT阅读器
  txtReader: {
    detectEncoding: (filePath: string) => Promise<any>
    getFileInfo: (filePath: string) => Promise<any>
    readFile: (filePath: string, encoding?: string) => Promise<any>
    createReader: (readerId: string, filePath: string, config?: any) => Promise<any>
    destroyReader: (readerId: string) => Promise<boolean>
    getPageContent: (readerId: string, pageNumber: number) => Promise<string>
    search: (readerId: string, query: string, options?: any) => Promise<any[]>
  }

  // EPUB阅读器 (现代化重新实现)
  epubReader: {
    parseEpub: (filePath: string) => Promise<any>
    getFileInfo: (filePath: string) => Promise<any>
    createReader: (readerId: string, filePath: string, config?: any) => Promise<any>
    destroyReader: (readerId: string) => Promise<boolean>
    getChapter: (readerId: string, chapterIndex: number) => Promise<any>
    getToc: (readerId: string) => Promise<any>
    search: (readerId: string, query: string) => Promise<any[]>
    navigate: (readerId: string, action: string, params?: any) => Promise<any>
    getStatus: (readerId: string) => Promise<any>
    updatePosition: (readerId: string, position: any) => Promise<any>
    getCreationProgress: (readerId: string) => Promise<any>
  }

  // 通用invoke方法
  invoke: (channel: string, ...args: any[]) => Promise<any>

  // 窗口操作
  window: {
    minimize: () => void
    maximize: () => void
    close: () => void
    toggleFullscreen: () => void
  }

  // 系统信息
  system: {
    platform: string
    version: string
  }
}

// 创建安全的API对象
const electronAPI: ElectronAPI = {
  // 书籍管理API
  book: {
    add: (filePath: string) => ipcRenderer.invoke('book:add', filePath),
    addWithDetails: (bookData: any) => ipcRenderer.invoke('book:add-with-details', bookData),
    extractMetadata: (filePath: string) => ipcRenderer.invoke('book:extract-metadata', filePath),
    remove: (bookId: string) => ipcRenderer.invoke('book:remove', bookId),
    list: () => ipcRenderer.invoke('book:list'),
    get: (bookId: string) => ipcRenderer.invoke('book:get', bookId),
    updateProgress: (bookId: string, progress: number, currentPage?: number) =>
      ipcRenderer.invoke('book:update-progress', bookId, progress, currentPage),
    updateInfo: (bookId: string, bookData: Partial<BookInfo>) =>
      ipcRenderer.invoke('book:update-info', bookId, bookData),
    setFavorite: (bookId: string, isFavorite: boolean) =>
      ipcRenderer.invoke('book:set-favorite', bookId, isFavorite),
    getCover: (coverPath: string) => ipcRenderer.invoke('book:get-cover', coverPath)
  },

  // 书签管理API
  bookmark: {
    add: (bookmark: any) => ipcRenderer.invoke('bookmark:add', bookmark),
    remove: (bookmarkId: string) => ipcRenderer.invoke('bookmark:remove', bookmarkId),
    list: (bookId: string) => ipcRenderer.invoke('bookmark:list', bookId),
    update: (bookmarkId: string, updates: any) => 
      ipcRenderer.invoke('bookmark:update', bookmarkId, updates)
  },

  // 笔记管理API
  note: {
    add: (note: any) => ipcRenderer.invoke('note:add', note),
    remove: (noteId: string) => ipcRenderer.invoke('note:remove', noteId),
    list: (bookId: string) => ipcRenderer.invoke('note:list', bookId),
    update: (noteId: string, updates: any) => 
      ipcRenderer.invoke('note:update', noteId, updates)
  },

  // 设置管理API
  settings: {
    get: () => ipcRenderer.invoke('settings:get'),
    update: (settings: any) => ipcRenderer.invoke('settings:update', settings)
  },

  // 文件操作API
  file: {
    read: (filePath: string) => ipcRenderer.invoke('file:read', filePath),
    readAsBase64: (filePath: string) => ipcRenderer.invoke('file:read-as-base64', filePath),
    exists: (filePath: string) => ipcRenderer.invoke('file:exists', filePath),
    select: (filters?: Electron.FileFilter[]) => ipcRenderer.invoke('file:select', filters),
    getInfo: (filePath: string) => ipcRenderer.invoke('file:get-info', filePath),
    selectImage: () => ipcRenderer.invoke('file:select-image'),
    validateImage: (filePath: string) => ipcRenderer.invoke('file:validate-image', filePath)
  },

  // TXT阅读器API
  txtReader: {
    detectEncoding: (filePath: string) => ipcRenderer.invoke('txt-reader:detect-encoding', filePath),
    getFileInfo: (filePath: string) => ipcRenderer.invoke('txt-reader:get-file-info', filePath),
    readFile: (filePath: string, encoding?: string) => ipcRenderer.invoke('txt-reader:read-file', filePath, encoding),
    createReader: (readerId: string, filePath: string, config?: any) =>
      ipcRenderer.invoke('txt-reader:create', readerId, filePath, config),
    destroyReader: (readerId: string) => ipcRenderer.invoke('txt-reader:destroy', readerId),
    getPageContent: (readerId: string, pageNumber: number) =>
      ipcRenderer.invoke('txt-reader:get-page-content', readerId, pageNumber),
    search: (readerId: string, query: string, options?: any) =>
      ipcRenderer.invoke('txt-reader:search', readerId, query, options)
  },

  // EPUB阅读器API (现代化重新实现)
  epubReader: {
    parseEpub: (filePath: string) => ipcRenderer.invoke('epub-reader:parse-epub', filePath),
    getFileInfo: (filePath: string) => ipcRenderer.invoke('epub-reader:get-file-info', filePath),
    createReader: (readerId: string, filePath: string, config?: any) =>
      ipcRenderer.invoke('epub-reader:create', readerId, filePath, config),
    destroyReader: (readerId: string) => ipcRenderer.invoke('epub-reader:destroy', readerId),
    getChapter: (readerId: string, chapterIndex: number) =>
      ipcRenderer.invoke('epub-reader:get-chapter', readerId, chapterIndex),
    getToc: (readerId: string) => ipcRenderer.invoke('epub-reader:get-toc', readerId),
    search: (readerId: string, query: string) =>
      ipcRenderer.invoke('epub-reader:search', readerId, query),
    navigate: (readerId: string, action: string, params?: any) =>
      ipcRenderer.invoke('epub-reader:navigate', readerId, action, params),
    getStatus: (readerId: string) => ipcRenderer.invoke('epub-reader:get-status', readerId),
    updatePosition: (readerId: string, position: any) =>
      ipcRenderer.invoke('epub-reader:update-position', readerId, position),
    getCreationProgress: (readerId: string) =>
      ipcRenderer.invoke('epub-reader:get-creation-progress', readerId)
  },

  // 通用invoke方法，用于向后兼容
  invoke: (channel: string, ...args: any[]) => ipcRenderer.invoke(channel, ...args),

  // 窗口操作API
  window: {
    minimize: () => ipcRenderer.invoke('window:minimize'),
    maximize: () => ipcRenderer.invoke('window:maximize'),
    close: () => ipcRenderer.invoke('window:close'),
    toggleFullscreen: () => ipcRenderer.invoke('window:toggle-fullscreen')
  },

  // 主题操作API
  theme: {
    set: (themeId: string) => ipcRenderer.invoke('theme:set', themeId),
    get: () => ipcRenderer.invoke('theme:get'),
    list: () => ipcRenderer.invoke('theme:list'),
    onChange: (callback: (themeId: string) => void) => {
      ipcRenderer.on('theme:change', (_, themeId) => callback(themeId))
    },
    removeChangeListener: () => {
      ipcRenderer.removeAllListeners('theme:change')
    }
  },

  // 阅读器专用API
  reader: {
    getBook: (bookId: string) => ipcRenderer.invoke('reader:get-book', bookId),
    getProgress: (bookId: string) => ipcRenderer.invoke('reader:get-progress', bookId),
    updateProgress: (bookId: string, progress: number, currentPage?: number) =>
      ipcRenderer.invoke('reader:update-progress', bookId, progress, currentPage),
    checkFile: (filePath: string) => ipcRenderer.invoke('reader:check-file', filePath),
    getBooks: () => ipcRenderer.invoke('reader:get-books')
  },

  // 系统信息API
  system: {
    platform: process.platform,
    version: process.versions.electron
  }
}

// 通过contextBridge安全地暴露API到渲染进程
contextBridge.exposeInMainWorld('electronAPI', electronAPI)

// 类型声明，供TypeScript使用
declare global {
  interface Window {
    electronAPI: ElectronAPI
  }
}
