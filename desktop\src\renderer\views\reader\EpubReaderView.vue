<template>
  <div class="epub-reader-container">
    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-container">
      <el-card class="loading-card">
        <div class="loading-content">
          <el-progress
            :percentage="loadingProgress"
            :status="error ? 'exception' : 'success'"
            :stroke-width="8"
          />
          <p class="loading-text">{{ loadingMessage }}</p>
          <p v-if="error" class="error-text">{{ error }}</p>
          <div class="loading-actions">
            <el-button @click="handleGoBack">返回书库</el-button>
            <el-button v-if="error" @click="retryLoad" type="primary">重试</el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 主阅读界面 -->
    <div v-else-if="isReady" class="reader-main">
      <!-- 顶部工具栏 -->
      <div class="reader-toolbar">
        <div class="toolbar-left">
          <el-button @click="handleGoBack" :icon="ArrowLeft" circle />
          <span class="book-title">{{ bookInfo?.title || '未知书籍' }}</span>
        </div>
        <div class="toolbar-center">
          <!-- 上一章按钮 -->
          <el-button
            @click="previousChapter"
            :disabled="currentChapterIndex <= 0"
            :icon="ArrowLeft"
            circle
            size="small"
            title="上一章"
          />

          <!-- 章节信息和阅读进度 -->
          <div class="chapter-progress-info">
            <span class="chapter-info">
              第 {{ currentChapterIndex + 1 }} 章 / 共 {{ totalChapters }} 章
            </span>
            <span class="reading-progress">{{ Math.round(overallProgress) }}%</span>
          </div>

          <!-- 下一章按钮 -->
          <el-button
            @click="nextChapter"
            :disabled="currentChapterIndex >= totalChapters - 1"
            :icon="ArrowRight"
            circle
            size="small"
            title="下一章"
          />
        </div>
        <div class="toolbar-right">
          <el-button @click="showTocPanel = true" :icon="List" circle title="目录" />
          <el-button @click="showSearchPanel = true" :icon="Search" circle title="搜索" />
          <el-button @click="showSettingsPanel = true" :icon="Setting" circle title="设置" />
        </div>
      </div>

      <!-- 阅读内容区域 -->
      <div class="reader-content" ref="contentContainer">
        <div class="chapter-container">
          <h2 class="chapter-title">{{ currentChapter?.title }}</h2>
          <div
            class="chapter-content"
            :style="contentStyle"
            v-html="processedContent"
            @click="handleContentClick"
          />
        </div>
      </div>

      <!-- 底部导航栏已移至顶部工具栏，此处保留注释以备将来需要 -->
      <!--
      <div class="reader-navigation">
        <el-button
          @click="previousChapter"
          :disabled="currentChapterIndex <= 0"
          :icon="ArrowLeft"
        >
          上一章
        </el-button>
        <div class="progress-info">
          <span>{{ Math.round(overallProgress) }}%</span>
          <el-progress
            :percentage="overallProgress"
            :show-text="false"
            :stroke-width="4"
          />
        </div>
        <el-button
          @click="nextChapter"
          :disabled="currentChapterIndex >= totalChapters - 1"
          :icon="ArrowRight"
        >
          下一章
        </el-button>
      </div>
      -->
    </div>

    <!-- 目录面板 -->
    <el-drawer
      v-model="showTocPanel"
      title="目录"
      direction="ltr"
      size="400px"
    >
      <div class="toc-container">
        <div
          v-for="(item, index) in tocItems"
          :key="item.id"
          class="toc-item"
          :class="{ active: (item.chapterIndex ?? index) === currentChapterIndex }"
          @click="goToChapter(item.chapterIndex ?? index)"
        >
          <span class="toc-title">{{ item.title }}</span>
          <span class="toc-level">{{ item.level }}</span>
        </div>
      </div>
    </el-drawer>

    <!-- 搜索面板 -->
    <el-drawer
      v-model="showSearchPanel"
      title="搜索"
      direction="rtl"
      size="400px"
    >
      <div class="search-container">
        <el-input
          v-model="searchQuery"
          placeholder="输入搜索关键词"
          @keyup.enter="performSearch"
          clearable
        >
          <template #append>
            <el-button @click="performSearch" :icon="Search" />
          </template>
        </el-input>

        <div v-if="searchResults.length > 0" class="search-results">
          <p class="results-count">找到 {{ searchResults.length }} 个结果</p>
          <div
            v-for="(result, index) in searchResults"
            :key="index"
            class="search-result-item"
            @click="goToSearchResult(result)"
          >
            <div class="result-context">{{ result.context }}</div>
            <div class="result-info">
              第{{ result.chapterIndex + 1 }}章 - {{ result.chapterTitle }}
            </div>
          </div>
        </div>

        <div v-else-if="searchQuery && !isSearching" class="no-results">
          <el-empty description="未找到匹配的内容" />
        </div>
      </div>
    </el-drawer>

    <!-- 设置面板 -->
    <el-drawer
      v-model="showSettingsPanel"
      title="阅读设置"
      direction="rtl"
      size="350px"
    >
      <div class="settings-container">
        <div class="setting-group">
          <h4>字体设置</h4>
          <div class="setting-item">
            <label>字体大小</label>
            <el-slider
              v-model="readerSettings.fontSize"
              :min="12"
              :max="24"
              :step="1"
              @change="updateContentStyle"
            />
          </div>
          <div class="setting-item">
            <label>行高</label>
            <el-slider
              v-model="readerSettings.lineHeight"
              :min="1.2"
              :max="2.0"
              :step="0.1"
              @change="updateContentStyle"
            />
          </div>
        </div>

        <div class="setting-group">
          <h4>主题设置</h4>
          <el-radio-group v-model="readerSettings.theme" @change="updateContentStyle">
            <el-radio value="light">浅色</el-radio>
            <el-radio value="dark">深色</el-radio>
            <el-radio value="sepia">护眼</el-radio>
          </el-radio-group>
        </div>

        <div class="setting-group">
          <h4>布局设置</h4>
          <div class="setting-item">
            <label>最大宽度</label>
            <el-slider
              v-model="readerSettings.maxWidth"
              :min="600"
              :max="1200"
              :step="50"
              @change="updateContentStyle"
            />
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  ArrowLeft,
  ArrowRight,
  List,
  Search,
  Setting
} from '@element-plus/icons-vue'

// Props
interface Props {
  /** 书籍ID */
  bookId: string
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'go-back': []
}>()

// 响应式数据
const isLoading = ref(true)
const isReady = ref(false)
const error = ref('')
const loadingProgress = ref(0)
const loadingMessage = ref('正在加载...')

const epubReaderId = ref('')
const currentBook = ref<any>(null)
const bookInfo = ref<any>(null)
const tocItems = ref<any[]>([])
const currentChapter = ref<any>(null)
const currentChapterIndex = ref(0)
const totalChapters = ref(0)
const overallProgress = ref(0)

// 面板状态
const showTocPanel = ref(false)
const showSearchPanel = ref(false)
const showSettingsPanel = ref(false)

// 搜索相关
const searchQuery = ref('')
const searchResults = ref<any[]>([])
const isSearching = ref(false)

// 阅读设置
const readerSettings = ref({
  fontSize: 16,
  lineHeight: 1.6,
  theme: 'light',
  maxWidth: 800,
  fontFamily: 'system-ui, -apple-system, sans-serif'
})

// 计算属性
const contentStyle = computed(() => ({
  fontSize: `${readerSettings.value.fontSize}px`,
  lineHeight: readerSettings.value.lineHeight,
  maxWidth: `${readerSettings.value.maxWidth}px`,
  fontFamily: readerSettings.value.fontFamily,
  margin: '0 auto',
  padding: '20px',
  backgroundColor: getThemeColors().background,
  color: getThemeColors().text
}))

// 处理章节内容，为图片添加错误处理和加载状态
const processedContent = computed(() => {
  if (!currentChapter.value?.content) return ''

  // 为图片添加错误处理和加载状态指示
  return currentChapter.value.content.replace(
    /<img([^>]*?)>/gi,
    (match, attributes) => {
      // 提取src属性用于调试
      const srcMatch = attributes.match(/src=["']([^"']+)["']/)
      const src = srcMatch ? srcMatch[1] : ''

      return `
        <div class="image-container" data-src="${src}">
          <div class="image-loading" style="display: block;">
            <div class="loading-spinner"></div>
            <div class="loading-text">图片加载中...</div>
          </div>
          <img${attributes}
            class="chapter-image"
            style="display: none; opacity: 0; transition: opacity 0.3s ease;"
            onload="
              this.style.display='block';
              this.style.opacity='1';
              this.parentElement.querySelector('.image-loading').style.display='none';
              this.parentElement.querySelector('.image-error').style.display='none';
            "
            onerror="
              this.style.display='none';
              this.parentElement.querySelector('.image-loading').style.display='none';
              this.parentElement.querySelector('.image-error').style.display='block';
            "
          />
          <div class="image-error" style="display: none;">
            <div class="error-icon">📷</div>
            <div class="error-text">图片加载失败</div>
            <div class="error-details">路径: ${src}</div>
          </div>
        </div>
      `
    }
  )
})

// 主题颜色
function getThemeColors() {
  const themes = {
    light: { background: '#ffffff', text: '#333333' },
    dark: { background: '#1a1a1a', text: '#e0e0e0' },
    sepia: { background: '#f7f3e9', text: '#5c4b37' }
  }
  return themes[readerSettings.value.theme as keyof typeof themes]
}

// 生命周期
onMounted(async () => {
  await loadEpubBook()
})

onUnmounted(() => {
  cleanup()
})

// 方法实现
async function loadEpubBook() {
  try {
    isLoading.value = true
    error.value = ''
    loadingProgress.value = 0
    loadingMessage.value = '正在初始化...'

    console.log(`EpubReaderView: 开始加载书籍 ${props.bookId}`)

    // 等待API可用
    await waitForAPI()
    loadingProgress.value = 10
    loadingMessage.value = '获取书籍信息...'

    // 获取书籍信息
    currentBook.value = await window.electronAPI.reader.getBook(props.bookId)
    if (!currentBook.value) {
      throw new Error('未找到指定的书籍')
    }

    loadingProgress.value = 30
    loadingMessage.value = '准备EPUB阅读器...'

    // 获取EPUB文件路径
    const filePath = currentBook.value.file_path || currentBook.value.filePath
    if (!filePath) {
      throw new Error(`书籍文件路径无效: ${filePath}`)
    }

    // 创建EPUB阅读器实例
    const readerId = `epub-reader-${props.bookId}-${Date.now()}`
    epubReaderId.value = readerId

    loadingProgress.value = 50
    loadingMessage.value = '创建阅读器实例...'

    const readerConfig = {
      enableImageLoading: true,
      enableStyleProcessing: true,
      enableToc: true,
      enableSearch: true
    }

    const createResult = await window.electronAPI.epubReader.createReader(readerId, filePath, readerConfig)

    if (!createResult?.success) {
      throw new Error(createResult?.error || '创建阅读器失败')
    }

    loadingProgress.value = 70
    loadingMessage.value = '加载目录信息...'

    // 获取目录信息
    const tocData = await window.electronAPI.epubReader.getToc(readerId)
    if (tocData.success) {
      // 处理嵌套的目录结构，提取实际的章节列表
      const toc = tocData.toc || tocData.chapters || []
      if (toc.length > 0 && toc[0].children) {
        // 如果有嵌套结构，使用children作为章节列表
        tocItems.value = toc[0].children
      } else {
        // 否则直接使用toc作为章节列表
        tocItems.value = toc
      }
      bookInfo.value = tocData.bookInfo
      totalChapters.value = tocItems.value.length

      console.log(`EpubReaderView: 加载目录完成，章节数: ${totalChapters.value}`)
      console.log('EpubReaderView: 目录项:', tocItems.value)
    }

    loadingProgress.value = 90
    loadingMessage.value = '加载第一章...'

    // 加载第一章内容
    if (totalChapters.value > 0) {
      await loadChapterContent(0)
    }

    loadingProgress.value = 100
    loadingMessage.value = '加载完成'

    // 延迟一下显示完成状态
    await new Promise(resolve => setTimeout(resolve, 500))

    isLoading.value = false
    isReady.value = true

    console.log('EpubReaderView: EPUB书籍加载完成')

  } catch (err) {
    console.error('EpubReaderView: 加载失败:', err)
    error.value = err instanceof Error ? err.message : '加载失败'
    loadingMessage.value = '加载失败'
  }
}

async function waitForAPI(maxRetries = 10, delay = 100): Promise<void> {
  for (let i = 0; i < maxRetries; i++) {
    if (window.electronAPI?.reader?.getBook && window.electronAPI?.epubReader?.createReader) {
      return
    }
    await new Promise(resolve => setTimeout(resolve, delay))
  }
  throw new Error('EPUB阅读器API初始化超时，请稍后重试')
}

async function loadChapterContent(chapterIndex: number) {
  try {
    if (!epubReaderId.value) {
      throw new Error('阅读器未初始化')
    }

    const result = await window.electronAPI.epubReader.getChapter(epubReaderId.value, chapterIndex)

    if (result?.success && result.chapter) {
      currentChapter.value = result.chapter
      currentChapterIndex.value = chapterIndex

      // 计算进度
      overallProgress.value = totalChapters.value > 0 ?
        ((chapterIndex + 1) / totalChapters.value) * 100 : 0

      // 滚动到顶部
      await nextTick()
      const container = document.querySelector('.reader-content')
      if (container) {
        container.scrollTop = 0
      }

      // 保存阅读进度
      await saveReadingProgress()

    } else {
      throw new Error(result?.error || '获取章节内容失败')
    }

  } catch (err) {
    console.error('加载章节失败:', err)
    ElMessage.error('加载章节失败')
  }
}

async function previousChapter() {
  if (currentChapterIndex.value > 0) {
    await loadChapterContent(currentChapterIndex.value - 1)
  }
}

async function nextChapter() {
  if (currentChapterIndex.value < totalChapters.value - 1) {
    await loadChapterContent(currentChapterIndex.value + 1)
  }
}

async function goToChapter(chapterIndex: number) {
  if (chapterIndex >= 0 && chapterIndex < totalChapters.value) {
    await loadChapterContent(chapterIndex)
    showTocPanel.value = false
  }
}

async function performSearch() {
  if (!searchQuery.value.trim() || !epubReaderId.value) {
    searchResults.value = []
    return
  }

  try {
    isSearching.value = true
    console.log('EpubReaderView: 执行搜索:', searchQuery.value.trim())

    const results = await window.electronAPI.epubReader.search(epubReaderId.value, searchQuery.value.trim())
    searchResults.value = results || []

    if (searchResults.value.length === 0) {
      ElMessage.info('未找到匹配的内容')
    }
  } catch (err) {
    console.error('搜索失败:', err)
    ElMessage.error('搜索失败')
  } finally {
    isSearching.value = false
  }
}

async function goToSearchResult(result: any) {
  await goToChapter(result.chapterIndex)
  showSearchPanel.value = false
  // TODO: 滚动到搜索结果位置并高亮显示
}

function updateContentStyle() {
  // 样式已通过computed属性自动更新
}

function handleContentClick(event: Event) {
  const target = event.target as HTMLElement

  // 处理图片点击
  if (target.tagName === 'IMG' && target.classList.contains('chapter-image')) {
    handleImageClick(target as HTMLImageElement)
    return
  }

  // 处理链接点击
  if (target.tagName === 'A') {
    event.preventDefault()
    // TODO: 处理内部链接跳转
  }
}

// 处理图片点击事件
function handleImageClick(img: HTMLImageElement) {
  // 如果图片加载失败，尝试重新加载
  if (img.style.display === 'none') {
    console.log('EpubReaderView: 尝试重新加载图片:', img.src)

    // 重置状态
    const container = img.parentElement
    if (container) {
      const loading = container.querySelector('.image-loading') as HTMLElement
      const error = container.querySelector('.image-error') as HTMLElement

      if (loading) loading.style.display = 'block'
      if (error) error.style.display = 'none'

      // 重新设置src触发重新加载
      const originalSrc = img.src
      img.src = ''
      setTimeout(() => {
        img.src = originalSrc
      }, 100)
    }
  } else {
    // 图片正常显示，可以添加预览功能
    console.log('EpubReaderView: 图片点击预览:', img.src)
    // TODO: 实现图片预览功能
  }
}

async function saveReadingProgress() {
  if (window.electronAPI?.reader?.updateProgress && currentBook.value) {
    try {
      await window.electronAPI.reader.updateProgress(
        props.bookId,
        overallProgress.value,
        currentChapterIndex.value + 1
      )
    } catch (error) {
      console.warn('保存阅读进度失败:', error)
    }
  }
}

async function retryLoad() {
  error.value = ''
  await loadEpubBook()
}

function handleGoBack() {
  emit('go-back')
}

function cleanup() {
  if (epubReaderId.value) {
    window.electronAPI.epubReader.destroyReader(epubReaderId.value)
      .catch(error => console.warn('清理阅读器失败:', error))
  }
}

// 调试功能：获取EPUB调试信息
async function getDebugInfo() {
  if (!epubReaderId.value) {
    console.warn('EpubReaderView: 阅读器未初始化，无法获取调试信息')
    return null
  }

  try {
    const debugInfo = await window.electronAPI.epubReader.getDebugInfo(epubReaderId.value)
    console.group('📚 EPUB调试信息')
    console.log('阅读器ID:', epubReaderId.value)
    console.log('当前书籍:', currentBook.value?.title)
    console.log('当前章节:', currentChapterIndex.value, currentChapter.value?.title)
    console.log('调试数据:', debugInfo)
    console.groupEnd()
    return debugInfo
  } catch (error) {
    console.error('获取调试信息失败:', error)
    return null
  }
}

// 开发模式下暴露调试功能到全局
if (process.env.NODE_ENV === 'development') {
  ;(window as any).epubDebug = {
    getDebugInfo,
    reloadCurrentChapter: () => loadChapterContent(currentChapterIndex.value),
    getCurrentChapter: () => currentChapter.value,
    getReaderSettings: () => readerSettings.value
  }
}
</script>

<style scoped>
.epub-reader-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f5f5f5;
}

.loading-card {
  width: 400px;
  text-align: center;
}

.loading-content {
  padding: 20px;
}

.loading-text {
  margin: 20px 0 10px;
  font-size: 16px;
  color: #666;
}

.error-text {
  margin: 10px 0;
  color: #f56565;
  font-size: 14px;
}

.loading-actions {
  margin-top: 20px;
  display: flex;
  gap: 10px;
  justify-content: center;
}

/* 主阅读界面样式 */
.reader-main {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 工具栏样式 */
.reader-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 20px;
  background-color: #fff;
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.book-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.toolbar-center {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
}

.chapter-progress-info {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 12px;
}

.chapter-info {
  font-size: 14px;
  color: #666;
  background-color: #f0f0f0;
  padding: 5px 12px;
  border-radius: 15px;
  white-space: nowrap;
}

.reading-progress {
  font-size: 12px;
  color: #409eff;
  font-weight: 600;
  background-color: #e8f4ff;
  padding: 2px 8px;
  border-radius: 10px;
  min-width: 35px;
  text-align: center;
}

.toolbar-right {
  display: flex;
  gap: 10px;
}

/* 阅读内容样式 */
.reader-content {
  flex: 1;
  overflow-y: auto;
  background-color: #fff;
  padding: 0;
}

.chapter-container {
  min-height: 100%;
  padding: 40px 20px;
}

.chapter-title {
  text-align: center;
  margin-bottom: 30px;
  font-size: 24px;
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #e0e0e0;
  padding-bottom: 15px;
}

.chapter-content {
  line-height: 1.8;
  font-size: 16px;
  color: #333;
  word-wrap: break-word;
}

.chapter-content :deep(p) {
  margin-bottom: 1em;
  text-indent: 2em;
}

/* 图片容器样式 */
.chapter-content :deep(.image-container) {
  margin: 20px auto;
  text-align: center;
  max-width: 100%;
  position: relative;
}

/* 图片样式 */
.chapter-content :deep(.chapter-image) {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 0 auto;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.chapter-content :deep(.chapter-image:hover) {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

/* 图片加载状态 */
.chapter-content :deep(.image-loading) {
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  padding: 40px 20px;
  color: #6c757d;
  font-size: 14px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.chapter-content :deep(.loading-spinner) {
  width: 32px;
  height: 32px;
  border: 3px solid #e9ecef;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.chapter-content :deep(.loading-text) {
  font-weight: 500;
  color: #495057;
}

/* 图片错误状态 */
.chapter-content :deep(.image-error) {
  background: #fff5f5;
  border: 2px dashed #fed7d7;
  border-radius: 8px;
  padding: 40px 20px;
  color: #c53030;
  font-size: 14px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.chapter-content :deep(.image-error:hover) {
  background: #fef5e7;
  border-color: #fbb6ce;
}

.chapter-content :deep(.error-icon) {
  font-size: 48px;
  margin-bottom: 12px;
  opacity: 0.7;
}

.chapter-content :deep(.error-text) {
  font-weight: 600;
  margin-bottom: 8px;
}

.chapter-content :deep(.error-details) {
  font-size: 12px;
  color: #718096;
  font-family: monospace;
  word-break: break-all;
  background: rgba(0, 0, 0, 0.05);
  padding: 8px;
  border-radius: 4px;
  margin-top: 8px;
}

/* 加载动画 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 图片淡入动画 */
.chapter-content :deep(.chapter-image[src^="data:"]) {
  animation: fadeInImage 0.5s ease-in-out;
}

@keyframes fadeInImage {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 底部导航样式 */
.reader-navigation {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 20px;
  background-color: #fff;
  border-top: 1px solid #e0e0e0;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
}

.progress-info {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 15px;
  margin: 0 20px;
}

.progress-info span {
  font-size: 14px;
  color: #666;
  min-width: 40px;
}

/* 目录面板样式 */
.toc-container {
  padding: 20px;
}

.toc-item {
  padding: 12px 15px;
  margin-bottom: 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  border-left: 3px solid transparent;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.toc-item:hover {
  background-color: #f0f0f0;
  border-left-color: #409eff;
}

.toc-item.active {
  background-color: #e6f7ff;
  border-left-color: #409eff;
  color: #409eff;
}

.toc-title {
  flex: 1;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.toc-level {
  font-size: 12px;
  color: #999;
  background-color: #f5f5f5;
  padding: 2px 6px;
  border-radius: 10px;
}

/* 搜索面板样式 */
.search-container {
  padding: 20px;
}

.search-results {
  margin-top: 20px;
}

.results-count {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e0e0e0;
}

.search-result-item {
  padding: 15px;
  margin-bottom: 10px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  cursor: pointer;
  transition: all 0.2s;
}

.search-result-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.result-context {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  line-height: 1.5;
}

.result-info {
  font-size: 12px;
  color: #999;
}

.no-results {
  margin-top: 40px;
  text-align: center;
}

/* 设置面板样式 */
.settings-container {
  padding: 20px;
}

.setting-group {
  margin-bottom: 30px;
}

.setting-group h4 {
  margin-bottom: 15px;
  font-size: 16px;
  color: #333;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 8px;
}

.setting-item {
  margin-bottom: 20px;
}

.setting-item label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .reader-toolbar {
    padding: 8px 15px;
  }

  .book-title {
    font-size: 16px;
    max-width: 150px;
  }

  .toolbar-center {
    gap: 10px;
  }

  .chapter-progress-info {
    gap: 8px;
  }

  .chapter-info {
    font-size: 12px;
    padding: 3px 8px;
  }

  .reading-progress {
    font-size: 11px;
    padding: 1px 6px;
    min-width: 30px;
  }

  .chapter-container {
    padding: 20px 15px;
  }

  .chapter-title {
    font-size: 20px;
  }

  .reader-navigation {
    padding: 10px 15px;
  }

  .progress-info {
    margin: 0 10px;
  }
}
</style>
