/**
 * 文件管理器
 * 负责文件系统操作和电子书文件处理
 */

import { promises as fs, existsSync, statSync } from 'fs'
import { join, extname, basename, dirname } from 'path'
import { app, dialog } from 'electron'
import { createHash } from 'crypto'

export class FileManager {
  private tempDir: string
  private coversDir: string

  constructor() {
    const userDataPath = app.getPath('userData')
    this.tempDir = join(userDataPath, 'temp')
    this.coversDir = join(userDataPath, 'covers')
  }

  /**
   * 初始化文件管理器
   */
  async initialize(): Promise<void> {
    try {
      // 创建必要的目录
      await this.ensureDirectoryExists(this.tempDir)
      await this.ensureDirectoryExists(this.coversDir)
      
      // 清理临时文件
      await this.cleanupTempFiles()
      
      console.log('文件管理器初始化完成')
    } catch (error) {
      console.error('文件管理器初始化失败:', error)
      throw error
    }
  }

  /**
   * 确保目录存在
   */
  private async ensureDirectoryExists(dirPath: string): Promise<void> {
    try {
      await fs.access(dirPath)
    } catch {
      await fs.mkdir(dirPath, { recursive: true })
    }
  }

  /**
   * 读取文件内容
   */
  async readFile(filePath: string): Promise<Buffer> {
    try {
      if (!existsSync(filePath)) {
        throw new Error(`文件不存在: ${filePath}`)
      }

      return await fs.readFile(filePath)
    } catch (error) {
      console.error('读取文件失败:', error)
      throw error
    }
  }

  /**
   * 读取文件内容并转换为base64字符串
   */
  async readFileAsBase64(filePath: string): Promise<string> {
    try {
      const buffer = await this.readFile(filePath)
      return buffer.toString('base64')
    } catch (error) {
      console.error('读取文件为base64失败:', error)
      throw error
    }
  }

  /**
   * 写入文件内容
   */
  async writeFile(filePath: string, data: Buffer): Promise<void> {
    try {
      // 确保目录存在
      const dir = dirname(filePath)
      await this.ensureDirectoryExists(dir)

      // 写入文件
      await fs.writeFile(filePath, data)
      console.log(`文件写入成功: ${filePath}`)
    } catch (error) {
      console.error('写入文件失败:', error)
      throw error
    }
  }

  /**
   * 检查文件是否存在
   */
  async fileExists(filePath: string): Promise<boolean> {
    try {
      // 修复路径格式：处理多种路径格式问题
      let normalizedPath = filePath

      // 处理Unix风格的绝对路径 /books/txt/...
      if (filePath.startsWith('/books/')) {
        normalizedPath = filePath.substring(1) // 移除开头的斜杠
        console.log(`FileManager: Unix路径格式修复 ${filePath} -> ${normalizedPath}`)
      }
      // 处理Windows绝对路径 D:/reader/desktop/books/txt/...
      else if (filePath.includes('/reader/desktop/books/')) {
        const booksIndex = filePath.indexOf('/books/')
        normalizedPath = filePath.substring(booksIndex + 1) // 提取 books/txt/... 部分
        console.log(`FileManager: Windows绝对路径格式修复 ${filePath} -> ${normalizedPath}`)
      }
      // 处理Windows绝对路径 D:\reader\desktop\books\txt\...
      else if (filePath.includes('\\reader\\desktop\\books\\')) {
        const booksIndex = filePath.indexOf('\\books\\')
        normalizedPath = filePath.substring(booksIndex + 1).replace(/\\/g, '/') // 提取并转换为正斜杠
        console.log(`FileManager: Windows反斜杠路径格式修复 ${filePath} -> ${normalizedPath}`)
      }

      await fs.access(normalizedPath)
      return true
    } catch {
      return false
    }
  }

  /**
   * 获取文件信息
   */
  async getFileInfo(filePath: string): Promise<{
    size: number
    format: string
    name: string
    hash: string
  }> {
    try {
      // 修复路径格式：处理多种路径格式问题
      let normalizedPath = filePath

      // 处理Unix风格的绝对路径 /books/txt/...
      if (filePath.startsWith('/books/')) {
        normalizedPath = filePath.substring(1) // 移除开头的斜杠
        console.log(`FileManager: Unix路径格式修复 ${filePath} -> ${normalizedPath}`)
      }
      // 处理Windows绝对路径 D:/reader/desktop/books/txt/...
      else if (filePath.includes('/reader/desktop/books/')) {
        const booksIndex = filePath.indexOf('/books/')
        normalizedPath = filePath.substring(booksIndex + 1) // 提取 books/txt/... 部分
        console.log(`FileManager: Windows绝对路径格式修复 ${filePath} -> ${normalizedPath}`)
      }
      // 处理Windows绝对路径 D:\reader\desktop\books\txt\...
      else if (filePath.includes('\\reader\\desktop\\books\\')) {
        const booksIndex = filePath.indexOf('\\books\\')
        normalizedPath = filePath.substring(booksIndex + 1).replace(/\\/g, '/') // 提取并转换为正斜杠
        console.log(`FileManager: Windows反斜杠路径格式修复 ${filePath} -> ${normalizedPath}`)
      }

      const stats = statSync(normalizedPath)
      const format = this.getFileFormat(normalizedPath)
      const name = basename(normalizedPath, extname(normalizedPath))
      const hash = await this.calculateFileHash(normalizedPath)

      return {
        size: stats.size,
        format,
        name,
        hash
      }
    } catch (error) {
      console.error('获取文件信息失败:', error)
      throw error
    }
  }

  /**
   * 获取文件格式
   */
  private getFileFormat(filePath: string): string {
    const ext = extname(filePath).toLowerCase()
    switch (ext) {
      case '.epub':
        return 'epub'
      case '.pdf':
        return 'pdf'
      case '.txt':
        return 'txt'
      case '.mobi':
        return 'mobi'
      default:
        throw new Error(`不支持的文件格式: ${ext}`)
    }
  }

  /**
   * 计算文件哈希值
   */
  private async calculateFileHash(filePath: string): Promise<string> {
    try {
      const buffer = await fs.readFile(filePath)
      return createHash('md5').update(buffer).digest('hex')
    } catch (error) {
      console.error('计算文件哈希失败:', error)
      throw error
    }
  }

  /**
   * 选择文件对话框
   */
  async selectFiles(filters?: Electron.FileFilter[]): Promise<string[]> {
    try {
      const defaultFilters: Electron.FileFilter[] = [
        { name: '电子书文件', extensions: ['epub', 'pdf', 'txt', 'mobi'] },
        { name: 'EPUB', extensions: ['epub'] },
        { name: 'PDF', extensions: ['pdf'] },
        { name: '文本文件', extensions: ['txt'] },
        { name: 'MOBI', extensions: ['mobi'] },
        { name: '所有文件', extensions: ['*'] }
      ]

      const result = await dialog.showOpenDialog({
        title: '选择电子书文件',
        filters: filters || defaultFilters,
        properties: ['openFile', 'multiSelections']
      })

      return result.canceled ? [] : result.filePaths
    } catch (error) {
      console.error('选择文件失败:', error)
      throw error
    }
  }

  /**
   * 选择文件夹对话框
   */
  async selectFolder(): Promise<string | null> {
    try {
      const result = await dialog.showOpenDialog({
        title: '选择文件夹',
        properties: ['openDirectory']
      })

      return result.canceled ? null : result.filePaths[0]
    } catch (error) {
      console.error('选择文件夹失败:', error)
      throw error
    }
  }

  /**
   * 扫描文件夹中的电子书文件
   */
  async scanBooksInFolder(folderPath: string): Promise<string[]> {
    try {
      const supportedExtensions = ['.epub', '.txt', '.mobi']
      const bookFiles: string[] = []

      const scanDirectory = async (dirPath: string): Promise<void> => {
        const entries = await fs.readdir(dirPath, { withFileTypes: true })

        for (const entry of entries) {
          const fullPath = join(dirPath, entry.name)

          if (entry.isDirectory()) {
            // 递归扫描子目录
            await scanDirectory(fullPath)
          } else if (entry.isFile()) {
            const ext = extname(entry.name).toLowerCase()
            if (supportedExtensions.includes(ext)) {
              bookFiles.push(fullPath)
            }
          }
        }
      }

      await scanDirectory(folderPath)
      return bookFiles
    } catch (error) {
      console.error('扫描文件夹失败:', error)
      throw error
    }
  }

  /**
   * 保存封面图片
   */
  async saveCover(bookId: string, coverData: Buffer, format: string = 'jpg'): Promise<string> {
    try {
      const coverFileName = `${bookId}.${format}`
      const coverPath = join(this.coversDir, coverFileName)
      
      await fs.writeFile(coverPath, coverData)
      return coverPath
    } catch (error) {
      console.error('保存封面失败:', error)
      throw error
    }
  }

  /**
   * 删除封面图片
   */
  async deleteCover(coverPath: string): Promise<void> {
    try {
      if (await this.fileExists(coverPath)) {
        await fs.unlink(coverPath)
      }
    } catch (error) {
      console.error('删除封面失败:', error)
      // 不抛出错误，因为封面删除失败不应该影响主要功能
    }
  }

  /**
   * 选择图片文件对话框
   */
  async selectImageFile(): Promise<string | null> {
    try {
      const result = await dialog.showOpenDialog({
        title: '选择封面图片',
        filters: [
          { name: '图片文件', extensions: ['jpg', 'jpeg', 'png', 'gif', 'webp'] },
          { name: 'JPEG', extensions: ['jpg', 'jpeg'] },
          { name: 'PNG', extensions: ['png'] },
          { name: '所有文件', extensions: ['*'] }
        ],
        properties: ['openFile']
      })

      return result.canceled ? null : result.filePaths[0]
    } catch (error) {
      console.error('选择图片文件失败:', error)
      throw error
    }
  }

  /**
   * 压缩和处理封面图片
   */
  async processCoverImage(imagePath: string, maxWidth: number = 400, maxHeight: number = 600): Promise<Buffer> {
    try {
      const imageBuffer = await fs.readFile(imagePath)

      // 如果图片小于限制，直接返回
      if (imageBuffer.length <= 1024 * 1024) { // 1MB以下直接返回
        return imageBuffer
      }

      // TODO: 如果需要图片压缩，可以安装 sharp 包
      // const sharp = require('sharp')
      // const processedBuffer = await sharp(imageBuffer)
      //   .resize(maxWidth, maxHeight, {
      //     fit: 'inside',
      //     withoutEnlargement: true
      //   })
      //   .jpeg({ quality: 85 })
      //   .toBuffer()
      // return processedBuffer

      // 临时返回原始图片
      return imageBuffer
    } catch (error) {
      console.error('处理封面图片失败:', error)
      // 如果处理失败，返回原始图片
      return await fs.readFile(imagePath)
    }
  }

  /**
   * 验证图片文件
   */
  async validateImageFile(filePath: string): Promise<{
    isValid: boolean
    error?: string
    info?: {
      size: number
      type: string
    }
  }> {
    try {
      const stats = await fs.stat(filePath)

      // 检查文件大小（限制为10MB）
      if (stats.size > 10 * 1024 * 1024) {
        return {
          isValid: false,
          error: '图片文件不能超过10MB'
        }
      }

      // 检查文件扩展名
      const ext = extname(filePath).toLowerCase()
      const allowedExts = ['.jpg', '.jpeg', '.png', '.gif', '.webp']

      if (!allowedExts.includes(ext)) {
        return {
          isValid: false,
          error: '不支持的图片格式'
        }
      }

      return {
        isValid: true,
        info: {
          size: stats.size,
          type: ext.substring(1)
        }
      }
    } catch (error) {
      return {
        isValid: false,
        error: '无法读取图片文件'
      }
    }
  }

  /**
   * 清理临时文件
   */
  async cleanupTempFiles(): Promise<void> {
    try {
      if (await this.fileExists(this.tempDir)) {
        const files = await fs.readdir(this.tempDir)
        for (const file of files) {
          await fs.unlink(join(this.tempDir, file))
        }
      }
      console.log('临时文件清理完成')
    } catch (error) {
      console.error('清理临时文件失败:', error)
    }
  }

  /**
   * 应用退出时的清理工作
   */
  async cleanup(): Promise<void> {
    await this.cleanupTempFiles()
  }

  /**
   * 获取临时目录路径
   */
  getTempDir(): string {
    return this.tempDir
  }

  /**
   * 获取封面目录路径
   */
  getCoversDir(): string {
    return this.coversDir
  }
}
